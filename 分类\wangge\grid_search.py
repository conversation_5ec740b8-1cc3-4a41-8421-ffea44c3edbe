# grid_search.py
from itertools import product
import pandas as pd, time, json, os
from experiment import run_once

# ------- 搜索网格，可按需改动 -------
param_space = {
    "iteration"     : [300, 500, 800,1000],    # 训练迭代数
    "batch_size"    : [64, 128, 256],
    "learning_rate" : [1e-3, 5e-4, 1e-4],
    "h_dim"         : [5, 10, 20],

    # ↓ 保持与 main.py 参数字典中其他键一致
    "data_name"     : ["twin"],
    "x_dim"         : [30],
}
# ------------------------------------

def dict_product(space):
    keys, vals = list(space.keys()), [space[k] for k in space]
    for combo in product(*vals):
        yield dict(zip(keys, combo))

results = []
os.makedirs("logs", exist_ok=True)

for i, p in enumerate(dict_product(param_space), 1):
    t0 = time.time()
    pehe, ate_err = run_once(p, seed=2025)
    dur = time.time() - t0
    rec = {**p, "pehe": pehe, "ate_err": ate_err, "time_s": dur}
    results.append(rec)

    print(f"[{i:02d}/{len(results)}] PEHE={pehe:.4f}, ATEerr={ate_err:.4f}, "
          f"time={dur/60:.1f}min, params={json.dumps(p)}", flush=True)

df = pd.DataFrame(results)
best = df.loc[df.pehe.idxmin()]
print("\n=== 最优参数组合 ===")
print(best)

df.to_csv("logs/grid_results.csv", index=False)
print("\n全部结果已写入 logs/grid_results.csv")
