# --- START OF FILE wgan_gp_stable.py ---

import os

os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["TF_CPP_MIN_LOG_LEVEL"] = '3'

import tensorflow as tf

gpus = tf.config.experimental.list_physical_devices('GPU')
if len(gpus) > 0:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        logical_gpus = tf.config.experimental.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPUs")
    except RuntimeError as e:
        print(e)

import tensorflow.keras.layers as layers
from utils import batch_generator, reparameterize
from metrics import PEHE, ATE
import numpy as np


def vganite_twin(train_x, train_t, train_y, train_potential_y, test_x, test_potential_y, y_test, test_t, parameters):
    batch_size = parameters['batch_size']
    iterations = parameters['iteration']
    h_dim = parameters['h_dim']
    base_learning_rate = parameters['learning_rate'] # Keep original LR for Stage 1 VAE/D
    x_dim = parameters['x_dim']
    lambda_gp = 10.0
    # --- Stability Parameters ---
    stage2_lr = 1e-4  # Lower learning rate for Stage 2 WGAN-GP
    gradient_clip_norm = 5.0 # Clip gradients to this norm for Stage 2

    class VAE(tf.keras.Model):
        def __init__(self):
            super(VAE, self).__init__()
            # Enhanced Encoder network with LeakyReLU and BatchNormalization
            # Increase hidden dimensions from 100 to 128 for more capacity

            # Encoder for z_t (treatment-specific)
            self.fc1 = layers.Dense(128)
            self.bn1 = layers.BatchNormalization()
            self.act1 = layers.LeakyReLU(alpha=0.2)
            self.dropout1 = layers.Dropout(0.3)  # Reduced dropout rate
            self.fc1_1 = layers.Dense(128)
            self.bn1_1 = layers.BatchNormalization()
            self.act1_1 = layers.LeakyReLU(alpha=0.2)
            self.fc12 = layers.Dense(128)
            self.bn12 = layers.BatchNormalization()
            self.act12 = layers.LeakyReLU(alpha=0.2)
            self.dropout2 = layers.Dropout(0.3)
            self.fc1_2 = layers.Dense(128)

            # Encoder for z_c (confounding factors)
            self.fc2 = layers.Dense(128)
            self.bn2 = layers.BatchNormalization()
            self.act2 = layers.LeakyReLU(alpha=0.2)
            self.dropout3 = layers.Dropout(0.3)
            self.fc2_1 = layers.Dense(128)
            self.bn2_1 = layers.BatchNormalization()
            self.act2_1 = layers.LeakyReLU(alpha=0.2)
            self.fc22 = layers.Dense(128)
            self.bn22 = layers.BatchNormalization()
            self.act22 = layers.LeakyReLU(alpha=0.2)
            self.dropout4 = layers.Dropout(0.3)
            self.fc2_2 = layers.Dense(128)

            # Encoder for z_y (outcome-specific)
            self.fc3 = layers.Dense(128)
            self.bn3 = layers.BatchNormalization()
            self.act3 = layers.LeakyReLU(alpha=0.2)
            self.dropout5 = layers.Dropout(0.3)
            self.fc3_1 = layers.Dense(128)
            self.bn3_1 = layers.BatchNormalization()
            self.act3_1 = layers.LeakyReLU(alpha=0.2)
            self.fc32 = layers.Dense(128)
            self.bn32 = layers.BatchNormalization()
            self.act32 = layers.LeakyReLU(alpha=0.2)
            self.dropout6 = layers.Dropout(0.3)
            self.fc3_2 = layers.Dense(128)

            # Enhanced Decoder network
            self.fc4 = layers.Dense(256)  # Increased capacity
            self.bn4 = layers.BatchNormalization()
            self.act4 = layers.LeakyReLU(alpha=0.2)
            self.fc4_1 = layers.Dense(128)
            self.bn4_1 = layers.BatchNormalization()
            self.act4_1 = layers.LeakyReLU(alpha=0.2)
            self.fc41 = layers.Dense(x_dim)  # Output x_dim

            # Treatment predictor from z_t, z_c
            self.fc5 = layers.Dense(64)
            self.bn5 = layers.BatchNormalization()
            self.act5 = layers.LeakyReLU(alpha=0.2)
            self.fc5_1 = layers.Dense(32)
            self.bn5_1 = layers.BatchNormalization()
            self.act5_1 = layers.LeakyReLU(alpha=0.2)
            self.fc51 = layers.Dense(1, activation=tf.nn.sigmoid)

            # Treatment predictor from z_c (for independence)
            self.fc8 = layers.Dense(64)
            self.bn8 = layers.BatchNormalization()
            self.act8 = layers.LeakyReLU(alpha=0.2)
            self.fc8_1 = layers.Dense(32)
            self.bn8_1 = layers.BatchNormalization()
            self.act8_1 = layers.LeakyReLU(alpha=0.2)
            self.fc81 = layers.Dense(1, activation=tf.nn.sigmoid)

            # Enhanced Stage 1 Generator (predicts potential outcomes)
            self.G_h1 = layers.Dense(units=h_dim * 4)
            self.G_bn1 = layers.BatchNormalization()
            self.G_act1 = layers.LeakyReLU(alpha=0.2)
            self.dropout7 = layers.Dropout(0.2)
            self.G_h2 = layers.Dense(units=h_dim * 2)
            self.G_bn2 = layers.BatchNormalization()
            self.G_act2 = layers.LeakyReLU(alpha=0.2)

            # Added residual connection
            self.G_res = layers.Dense(units=h_dim)
            self.G_res_bn = layers.BatchNormalization()

            self.G_h31 = layers.Dense(units=h_dim)
            self.G_bn3 = layers.BatchNormalization()
            self.G_act3 = layers.LeakyReLU(alpha=0.2)
            self.G_logit = layers.Dense(units=2)  # Output [Y(0), Y(1)] logits

        def encoder(self, x):
            # (保持不变)
            ht = self.fc1(x)
            ht = self.dropout1(ht, training=self.dropout1.training) # Pass training flag
            mu_t = tf.nn.relu(self.fc1_1(ht))
            log_var_t = self.fc12(x)
            log_var_t = self.dropout2(log_var_t, training=self.dropout2.training)
            log_var_t = self.fc1_2(log_var_t)

            hc = self.fc2(x)
            hc = self.dropout3(hc, training=self.dropout3.training)
            mu_c = tf.nn.relu(self.fc2_1(hc))
            log_var_c = self.fc22(x)
            log_var_c = self.dropout4(log_var_c, training=self.dropout4.training)
            log_var_c = self.fc2_2(log_var_c)

            hy = self.fc3(x)
            hy = self.dropout5(hy, training=self.dropout5.training)
            mu_y = tf.nn.relu(self.fc3_1(hy))
            log_var_y = self.fc32(x)
            log_var_y = self.dropout6(log_var_y, training=self.dropout6.training)
            log_var_y = self.fc3_2(log_var_y)

            return mu_t, log_var_t, mu_c, log_var_c, mu_y, log_var_y

        def decoder(self, z_t, z_c, z_y):
            # (保持不变)
            Z = tf.concat([z_t, z_c, z_y], axis=1)
            Z = self.fc4(Z)
            x = self.fc4_1(Z)
            x = self.fc41(x)
            return x

        def decoder_t(self, z_t, z_c):
            # (保持不变)
            t_hat = self.fc5(tf.concat([z_t, z_c], axis=1))
            t_hat = self.fc5_1(t_hat)
            t_hat = self.fc51(t_hat)
            return t_hat

        def treatment(self, z_c):
             # (保持不变)
            t_hat = self.fc8(z_c)
            t_hat = self.fc8_1(t_hat)
            t_hat = self.fc81(t_hat)
            return t_hat

        def generator(self, z_c, z_y):
             # (保持不变)
            inputs = tf.concat([z_c, z_y], axis=1)
            G_h1 = self.G_h1(inputs)
            G_h1 = self.dropout7(G_h1, training=self.dropout7.training)
            G_h2 = self.G_h2(G_h1)
            G_h31 = self.G_h31(G_h2)
            G_logit = self.G_logit(G_h31)
            return G_logit # [Y(0),Y(1)] logits

        def call(self, inputs, t, y, training=None):
             # (保持不变, 注意传递 training 参数给 Dropout)
            # Explicitly set training status for dropout layers
            self.dropout1.training = training
            self.dropout2.training = training
            self.dropout3.training = training
            self.dropout4.training = training
            self.dropout5.training = training
            self.dropout6.training = training
            self.dropout7.training = training

            mu_t, log_var_t, mu_c, log_var_c, mu_y, log_var_y = self.encoder(inputs)
            z_t = reparameterize(mu_t, log_var_t)
            z_c = reparameterize(mu_c, log_var_c)
            z_y = reparameterize(mu_y, log_var_y)

            # Optional: Latent vector normalization (保持不变)
            z_t = z_t / tf.sqrt(tf.reduce_sum(tf.square(z_t), axis=1, keepdims=True) + 1e-8) # Add epsilon
            z_c = z_c / tf.sqrt(tf.reduce_sum(tf.square(z_c), axis=1, keepdims=True) + 1e-8)
            z_y = z_y / tf.sqrt(tf.reduce_sum(tf.square(z_y), axis=1, keepdims=True) + 1e-8)

            x_hat = self.decoder(z_t, z_c, z_y)
            t_hat = self.decoder_t(z_t, z_c)         # Prediction of t from z_t, z_c
            y_hat_logits = self.generator(z_c, z_y)  # Prediction of [Y(0), Y(1)] logits
            treat_t_hat = self.treatment(z_c)        # Prediction of t from z_c (for independence loss)

            return z_c, z_y, x_hat, t_hat, y_hat_logits, mu_t, log_var_t, mu_c, log_var_c, mu_y, log_var_y, treat_t_hat

    # --- Stage 1 Discriminator (保持不变) ---
    class Discriminator(tf.keras.Model):
        # ... (Discriminator Class definition remains exactly the same) ...
        def __init__(self):
            super().__init__()
            # 与原始 vganite_twin.py 中的 discriminator 相同
            self.D_h1 = tf.keras.layers.Dense(units=10, activation=tf.nn.relu)
            self.D_h2 = tf.keras.layers.Dense(units=5, activation=tf.nn.relu)
            self.D_logit = tf.keras.layers.Dense(units=1) # Output logit for predicting treatment t

        def call(self, t, y, y_hat_logits): # y_hat_logits from VAE.generator
            # 使用 Sigmoid 将 logits 转为 [0,1] 范围内的“伪概率”
            # 注意：这里使用 sigmoid 后的值构建 y_bar，与原始代码一致
            y_hat_prob = tf.nn.sigmoid(y_hat_logits)
            y_hat_prob = tf.reshape(y_hat_prob, [-1, 2])

            y = tf.reshape(y, [-1, 1])
            t = tf.reshape(t, [-1, 1])

            # Construct y_bar using factual y and *generated* counterfactual (from y_hat_prob)
            input0 = (1. - t) * y + t * tf.reshape(y_hat_prob[:, 0], [-1, 1]) # If t=1, use generated Y(0)
            input1 = t * y + (1. - t) * tf.reshape(y_hat_prob[:, 1], [-1, 1]) # If t=0, use generated Y(1)
            y_bar = tf.concat([input0, input1], axis=1) # Shape: [batch, 2]

            # Predict treatment t based on y_bar
            D_h1 = self.D_h1(y_bar)
            D_h2 = self.D_h2(D_h1)
            D_logit = self.D_logit(D_h2) # Logit predicting t
            return D_logit


    # --- Stage 2 ITE Generator (保持不变) ---
    class ITE_Generator(tf.keras.Model):
        # ... (ITE_Generator Class definition remains exactly the same) ...
        def __init__(self):
            super().__init__()
            self.I_h1 = layers.Dense(units=h_dim, activation=tf.nn.relu)
            self.I_h2 = layers.Dense(units=h_dim, activation=tf.nn.relu)
            self.I_h3 = layers.Dense(units=h_dim, activation=tf.nn.relu)
            self.I_logit = layers.Dense(units=2) # Output [Y(0), Y(1)] logits

        def call(self, x, training=None):
             # Dropout is not used here, but good practice to include training flag if it were
            I_h1 = self.I_h1(x)
            I_h2 = self.I_h2(I_h1)
            I_h3 = self.I_h3(I_h2)
            I_logit = self.I_logit(I_h3)
            return I_logit

    # --- Stage 2 ITE Critic (WGAN-GP) (保持不变) ---
    class ITE_WGAN_GPCritic(tf.keras.Model):
        def __init__(self):
            super().__init__()
            self.DI_h1 = layers.Dense(units=h_dim * 4)
            self.norm1 = layers.LayerNormalization()
            self.act1 = layers.LeakyReLU(alpha=0.2)
            self.DI_h2 = layers.Dense(units=h_dim * 2)
            self.norm2 = layers.LayerNormalization()
            self.act2 = layers.LeakyReLU(alpha=0.2)
            self.DI_h3 = layers.Dense(units=h_dim)
            self.norm3 = layers.LayerNormalization()
            self.act3 = layers.LeakyReLU(alpha=0.2)
            self.DI_critic = layers.Dense(units=1)

        def call(self, x, y_pair):
            inputs = tf.concat([x, y_pair], axis=1)
            DI_h1 = self.act1(self.norm1(self.DI_h1(inputs)))
            DI_h2 = self.act2(self.norm2(self.DI_h2(DI_h1)))
            DI_h3 = self.act3(self.norm3(self.DI_h3(DI_h1)))
            DI_critic = self.DI_critic(DI_h3)
            return DI_critic

    # --- Gradient Penalty Function (加入 epsilon) ---
    def gradient_penalty(critic, x_batch, real_y_pair, fake_y_pair):
        batch_size_gp = tf.shape(x_batch)[0]
        alpha = tf.random.uniform([batch_size_gp, 1], 0., 1.)
        interpolated_y_pair = alpha * real_y_pair + (1 - alpha) * fake_y_pair

        with tf.GradientTape() as tape_gp:
            tape_gp.watch(interpolated_y_pair)
            critic_interpolated = critic(x_batch, interpolated_y_pair)

        grads = tape_gp.gradient(critic_interpolated, interpolated_y_pair)
        # Add safety check for grads being None
        if grads is None:
            tf.print("Warning: Gradients for GP calculation are None.")
            return tf.constant(0.0, dtype=tf.float32) # Return 0 penalty if grads are None

        # Calculate the norm of gradients with epsilon
        norm = tf.sqrt(tf.reduce_sum(tf.square(grads), axis=[1]) + 1e-8) # Added epsilon
        gp = tf.reduce_mean((norm - 1.0) ** 2)
        return gp

    # --- Instantiate Models ---
    model = VAE()
    D = Discriminator()
    ITE_G = ITE_Generator()
    ITE_C = ITE_WGAN_GPCritic()

    # --- Optimizers (修改 Stage 2 学习率) ---
    optimizer_vae = tf.keras.optimizers.Adam(learning_rate=base_learning_rate)
    optimizer_d = tf.keras.optimizers.Adam(learning_rate=base_learning_rate)
    # Use lower LR and recommended betas for Stage 2 WGAN-GP optimizers
    optimizer_ite_g = tf.keras.optimizers.Adam(learning_rate=stage2_lr, beta_1=0.5, beta_2=0.9)
    optimizer_ite_c = tf.keras.optimizers.Adam(learning_rate=stage2_lr, beta_1=0.5, beta_2=0.9)

    lambda_I_supervised = 75
    n_critic_stage2 = 5

    print("--- Start Training Stage 1: VAE + Original GAN ---")
    # ... (Stage 1 Training Loop remains exactly the same as in the previous wgan_gp.py) ...
    for epoch in range(1): # Assuming 1 epoch is sufficient for stage 1 based on original code
        for it in range(iterations):
            # --- Train Stage 1 Discriminator (Original GAN) ---
            # Original code trained D twice per G update
            d_loss_val = 0.0 # Initialize d_loss_val for logging
            for _ in range(2):
                with tf.GradientTape() as tape_d:
                    x, t, y = batch_generator(train_x, train_t, train_y, batch_size)
                    x = tf.cast(x, tf.float32)
                    t = tf.cast(t, tf.float32)
                    y = tf.cast(y, tf.float32)

                    # Get generated potential outcomes from VAE (run in training=False for D update?)
                    # Let's run VAE in training=True as gradients need to flow back eventually
                    _, _, _, _, y_hat_logits, _, _, _, _, _, _, _ = model(x, t, y, training=True)

                    # Discriminator tries to predict t from y_bar
                    D_logit = D(t, y, y_hat_logits) # Pass logits here

                    # Discriminator loss: Cross-entropy for predicting t
                    # Note: Using sigmoid_cross_entropy_with_logits handles logits directly
                    d_loss = tf.reduce_mean(tf.nn.sigmoid_cross_entropy_with_logits(labels=t, logits=D_logit))
                    d_loss_val = d_loss # Store for logging

                # Apply gradients to Discriminator D
                d_grads = tape_d.gradient(d_loss, D.trainable_variables)
                # Check if gradients exist before applying
                if all(g is not None for g in d_grads):
                   optimizer_d.apply_gradients(grads_and_vars=zip(d_grads, D.trainable_variables))
                else:
                    tf.print("Warning: None gradient detected for Stage 1 Discriminator.")


            # --- Train Stage 1 VAE (Generator) ---
            with tf.GradientTape() as tape_vae:
                x, t, y = batch_generator(train_x, train_t, train_y, batch_size)
                x = tf.cast(x, tf.float32)
                t = tf.cast(t, tf.float32)
                y = tf.cast(y, tf.float32)

                # VAE Forward pass (training=True for gradients)
                z_c, z_y, x_hat, t_hat, y_hat_logits, mu_t, log_var_t, mu_c, log_var_c, mu_y, log_var_y, treat_t_hat = model(x, t, y, training=True)

                # --- VAE Reconstruction and Regularization Losses ---
                rec_x_loss = tf.reduce_mean(tf.square(x_hat - x))
                rec_t_loss = -tf.reduce_mean(t * tf.math.log(t_hat + 1e-8) + (1.0 - t) * tf.math.log(1.0 - t_hat + 1e-8)) # Added epsilon
                treat_t_hat_loss = -tf.reduce_mean(t * tf.math.log(treat_t_hat + 1e-8) + (1.0 - t) * tf.math.log(1.0 - treat_t_hat + 1e-8)) # Added epsilon

                kl_div_t = -0.5 * tf.reduce_sum(log_var_t + 1 - mu_t ** 2 - tf.exp(log_var_t), axis=-1)
                kl_div_c = -0.5 * tf.reduce_sum(log_var_c + 1 - mu_c ** 2 - tf.exp(log_var_c), axis=-1)
                kl_div_y = -0.5 * tf.reduce_sum(log_var_y + 1 - mu_y ** 2 - tf.exp(log_var_y), axis=-1)
                kl_div_t = tf.reduce_mean(kl_div_t) # Mean over batch
                kl_div_c = tf.reduce_mean(kl_div_c)
                kl_div_y = tf.reduce_mean(kl_div_y)

                # --- VAE Generator Losses (Factual & Adversarial) ---
                # Factual Loss (Weighted MSE on observed outcome)
                p_t = tf.cast(tf.reduce_sum(t) / tf.cast(tf.shape(t)[0], tf.float32) + 1e-8, tf.float32) # Add epsilon
                w_t = t / (2. * p_t)
                w_c = (1. - t) / (2. * (1. - p_t + 1e-8)) # Epsilon for denominator
                # Using treat_t_hat for propensity score estimate pi_0
                pi_0 = t * treat_t_hat + (1.0 - t) * (1.0 - treat_t_hat)
                pi_0 = tf.clip_by_value(pi_0, 1e-6, 1.0 - 1e-6) # Clip propensity score
                # Original weight from vganite_twin.py:
                sample_weight = 1. * (1. + (1. - pi_0) / pi_0 * (p_t / (1. - p_t + 1e-8)) ** (2. * t - 1.)) * (w_t + w_c) # Reverted to original, added epsilon

                y_factual_logit = t * tf.reshape(y_hat_logits[:, 1], [-1, 1]) + (1. - t) * tf.reshape(y_hat_logits[:, 0], [-1, 1])
                # G_loss_Factual: Compare observed y with the corresponding predicted logit
                # Using MSE loss on logits here, as in original G_loss_Factual calculation
                G_loss_Factual = tf.reduce_mean(sample_weight * tf.square(y_factual_logit - y))

                # Adversarial Loss (Generator fools Discriminator D)
                # Use the D loss calculated in the D step (as in original code)
                # This assumes D hasn't changed significantly between D and G steps
                # Re-compute D's prediction for the current generator output
                D_logit_for_G = D(t, y, y_hat_logits)
                # Generator wants D to predict the *wrong* label for t
                # i.e., predict 0 if t=1, predict 1 if t=0. This is equivalent to maximizing D's loss.
                G_loss_GAN = tf.reduce_mean(tf.nn.sigmoid_cross_entropy_with_logits(labels=(1-t), logits=D_logit_for_G)) # Correct adversarial loss

                # --- Total VAE Loss ---
                # Note: KL weights are still 1e-5 as requested
                vae_loss = rec_x_loss + treat_t_hat_loss + \
                           1e-5 * rec_t_loss + \
                           1e-5 * (kl_div_t + kl_div_c + kl_div_y) + \
                           G_loss_GAN + 2 * G_loss_Factual # Original factor of 2 for factual loss

            # Apply gradients to VAE model
            vae_grads = tape_vae.gradient(vae_loss, model.trainable_variables)
            # Check if gradients exist before applying
            if all(g is not None for g in vae_grads):
                 # Optional: Gradient clipping if needed
                 # vae_grads, _ = tf.clip_by_global_norm(vae_grads, 5.0)
                 optimizer_vae.apply_gradients(zip(vae_grads, model.trainable_variables))
            else:
                tf.print("Warning: None gradient detected for Stage 1 VAE.")


            # --- Logging (Stage 1) ---
            if it % 100 == 0:
                 # (Logging code remains the same)
                # Evaluate on train set (using training=False)
                x_train_eval, t_train_eval, y_train_eval = train_x, train_t, train_y
                x_train_eval = tf.cast(x_train_eval, tf.float32)
                t_train_eval = tf.cast(t_train_eval, tf.float32)
                y_train_eval = tf.cast(y_train_eval, tf.float32)
                _, _, _, _, y_hat_train_logits, _, _, _, _, _, _, _ = model(x_train_eval, t_train_eval, y_train_eval, training=False)
                y_hat_train_prob = tf.nn.sigmoid(y_hat_train_logits)
                train_potential_y_tf = tf.cast(train_potential_y, tf.float32)
                train_pehe = PEHE(train_potential_y_tf, y_hat_train_prob)
                train_ate = ATE(train_potential_y_tf, y_hat_train_prob) # Use probs for ATE too
                train_ate_t_val = np.mean(train_potential_y[:, 1]) - np.mean(train_potential_y[:, 0])
                train_ate_hat_val = np.mean((y_hat_train_prob[:, 1].numpy()) - (y_hat_train_prob[:, 0].numpy())) # Use probs

                print(f"Stage 1 - Iter: {it}/{iterations}, VAE Loss: {vae_loss:.4f}, Factual Loss: {G_loss_Factual:.4f}, D Loss: {d_loss_val:.4f}")
                print(f"  Train PEHE: {train_pehe:.4f}, Train ATE: {train_ate:.4f} (True: {train_ate_t_val:.4f}, Est: {train_ate_hat_val:.4f})")

                # Evaluate on test set (using training=False)
                x_test_eval, t_test_eval, y_test_eval = test_x, test_t, y_test
                x_test_eval = tf.cast(x_test_eval, tf.float32)
                t_test_eval = tf.cast(t_test_eval, tf.float32)
                y_test_eval = tf.cast(y_test_eval, tf.float32)
                _, _, _, _, y_hat_test_logits, _, _, _, _, _, _, _ = model(x_test_eval, t_test_eval, y_test_eval, training=False)
                y_hat_test_prob = tf.nn.sigmoid(y_hat_test_logits)
                test_potential_y_tf = tf.cast(test_potential_y, tf.float32)
                test_pehe = PEHE(test_potential_y_tf, y_hat_test_prob)
                test_ate = ATE(test_potential_y_tf, y_hat_test_prob) # Use probs
                test_ate_t_val = np.mean(test_potential_y.numpy()[:, 1]) - np.mean(test_potential_y.numpy()[:, 0])
                test_ate_hat_val = np.mean((y_hat_test_prob[:, 1].numpy()) - (y_hat_test_prob[:, 0].numpy())) # Use probs

                print(f"  Test PEHE: {test_pehe:.4f}, Test ATE: {test_ate:.4f} (True: {test_ate_t_val:.4f}, Est: {test_ate_hat_val:.4f})")


    print("\n--- Start Training Stage 2: ITE Estimation WGAN-GP ---")
    # ... (Generating y_bar_prob_target and stage2_dataset remains the same) ...
    x_train_tf = tf.cast(train_x, tf.float32)
    t_train_tf = tf.cast(train_t, tf.float32)
    y_train_tf = tf.cast(train_y, tf.float32)
    _, _, _, _, y_bar_logits_from_stage1, _, _, _, _, _, _, _ = model(x_train_tf, t_train_tf, y_train_tf, training=False)
    # Use probabilities as the target distribution for Stage 2, consistent with original D input
    y_bar_prob_target = tf.nn.sigmoid(y_bar_logits_from_stage1)

    # Create dataset for Stage 2
    stage2_dataset = tf.data.Dataset.from_tensor_slices((x_train_tf, y_bar_prob_target)).shuffle(
        buffer_size=len(train_x)).batch(batch_size).prefetch(tf.data.AUTOTUNE)

    # Use tf.function for potential speedup and graph optimization in Stage 2 loop
    @tf.function
    def stage2_train_step(x_batch, y_bar_batch_real):
        # --- Train ITE Critic (WGAN-GP) ---
        for _ in range(n_critic_stage2):
            with tf.GradientTape() as tape_c:
                y_tilde_logits = ITE_G(x_batch, training=True)
                y_tilde_prob = tf.nn.sigmoid(y_tilde_logits)
                c_real = ITE_C(x_batch, y_bar_batch_real)
                c_fake = ITE_C(x_batch, y_tilde_prob)
                wass_dist = tf.reduce_mean(c_fake) - tf.reduce_mean(c_real)
                gp = gradient_penalty(ITE_C, x_batch, y_bar_batch_real, y_tilde_prob)
                c_loss = wass_dist + lambda_gp * gp

            c_grads = tape_c.gradient(c_loss, ITE_C.trainable_variables)
            # Apply gradient clipping for Critic
            c_grads_clipped, _ = tf.clip_by_global_norm(c_grads, gradient_clip_norm)
            # Check if gradients exist before applying
            if all(g is not None for g in c_grads_clipped):
                optimizer_ite_c.apply_gradients(zip(c_grads_clipped, ITE_C.trainable_variables))
            # else: # Printing inside tf.function can be tricky / slow
            #     tf.print("Warning: None gradient detected for Stage 2 Critic.")


        # --- Train ITE Generator (WGAN-GP) ---
        with tf.GradientTape() as tape_g:
            y_tilde_logits = ITE_G(x_batch, training=True)
            y_tilde_prob = tf.nn.sigmoid(y_tilde_logits)
            c_fake_for_g = ITE_C(x_batch, y_tilde_prob)
            g_loss_adversarial = -tf.reduce_mean(c_fake_for_g)
            g_loss_supervised = tf.reduce_mean(tf.abs(y_tilde_prob - y_bar_batch_real))
            g_loss = g_loss_adversarial + lambda_I_supervised * g_loss_supervised

        g_grads = tape_g.gradient(g_loss, ITE_G.trainable_variables)
        # Apply gradient clipping for Generator
        g_grads_clipped, _ = tf.clip_by_global_norm(g_grads, gradient_clip_norm)
         # Check if gradients exist before applying
        if all(g is not None for g in g_grads_clipped):
            optimizer_ite_g.apply_gradients(zip(g_grads_clipped, ITE_G.trainable_variables))
        # else:
        #     tf.print("Warning: None gradient detected for Stage 2 Generator.")

        return c_loss, wass_dist, gp, g_loss, g_loss_adversarial, g_loss_supervised

    # --- Stage 2 Training Loop ---
    for it in range(iterations):
        for x_batch, y_bar_batch_real in stage2_dataset:
             # Run the training step
             c_loss, wass_dist, gp, g_loss, g_loss_adversarial, g_loss_supervised = stage2_train_step(x_batch, y_bar_batch_real)

        # --- Logging (Stage 2) ---
        if it % 100 == 0 or it == iterations - 1:
             # Log losses from the last step's result (as Tensor values)
             # Use .numpy() to get values outside tf.function if needed for printing complex formats
            print(f'Stage 2 - Iter: {it}/{iterations}, ITE_C Loss: {c_loss.numpy():.4f} (Wass: {wass_dist.numpy():.4f}, GP: {gp.numpy():.4f}), ITE_G Loss: {g_loss.numpy():.4f} (Adv: {g_loss_adversarial.numpy():.4f}, Sup: {g_loss_supervised.numpy():.4f})')


    # --- Inference Phase (保持不变) ---
    test_x_tf = tf.cast(test_x, tf.float32)
    test_y_hat_logits = ITE_G(test_x_tf, training=False)
    test_y_hat = tf.nn.sigmoid(test_y_hat_logits)

    return test_y_hat, ITE_G

# --- END OF FILE wgan_gp_stable.py ---