import os

os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["TF_CPP_MIN_LOG_LEVEL"] = '3'
import tensorflow as tf
import numpy as np
import argparse
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, mean_squared_error, mean_absolute_error, roc_curve, \
    recall_score, precision_score
import matplotlib.pyplot as plt  # 导入 matplotlib
import matplotlib as mpl  # 导入 matplotlib 基础库

# --- Matplotlib 中文字体配置 ---
# 尝试设置常用中文字体，优先使用 SimHei 或 Microsoft YaHei
try:
    # mpl.rcParams['font.sans-serif'] = ['SimHei'] # 如果您明确知道系统中有 SimHei
    # 或者尝试多个可能的字体
    mpl.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'Source Han Sans CN',
                                       'sans-serif']
    mpl.rcParams['axes.unicode_minus'] = False  # 正确显示负号
    print("尝试设置中文字体成功 (SimHei/Microsoft YaHei/...).")
except Exception as e:
    print(f"Warning: 设置中文字体失败: {e}. 图表中的中文可能无法正确显示。")
    print("请考虑安装 SimHei, Microsoft YaHei 或其他支持中文的字体，或者在代码中直接指定字体路径。")

# --- （备选方案）直接指定字体路径 ---
# from matplotlib.font_manager import fontManager
# import os
# font_path = 'C:/Windows/Fonts/simhei.ttf' # 示例路径，请替换为您的字体实际路径
# if os.path.exists(font_path):
#     try:
#         fontManager.addfont(font_path)
#         mpl.rcParams['font.family'] = 'SimHei' # 替换为您添加的字体名
#         mpl.rcParams['axes.unicode_minus'] = False
#         print(f"成功加载并设置字体: {font_path}")
#     except Exception as e:
#         print(f"加载指定字体失败: {e}")
# else:
#     print(f"Warning: 指定的字体路径未找到: {font_path}")

import seaborn as sns  # 在设置完字体参数后导入 seaborn

from wgangp import vganite_twin
from metrics import PEHE, ATE
from datasets import IHDP, data_loading_twin, Syn


# --- 辅助函数：打印潜在结果的类别统计 (保持不变) ---
def print_potential_outcome_stats(po_data, po_name):
    if isinstance(po_data, tf.Tensor):
        po_data_np = po_data.numpy()
    else:
        po_data_np = np.array(po_data)
    if po_data_np.ndim != 2 or po_data_np.shape[1] != 2:
        print(f"错误: {po_name} 的期望维度是 (N, 2)，实际是 {po_data_np.shape}")
        return
    print(f"\n--- {po_name} (潜在结果 Y(0) 和 Y(1)) 类别统计 ---")
    y0_labels = po_data_np[:, 0]
    unique_y0, counts_y0 = np.unique(y0_labels, return_counts=True)
    total_y0 = len(y0_labels)
    print(f"  {po_name} - Y(0) (第一列):")
    print(f"    总样本数: {total_y0}")
    positive_y0_count = counts_y0[unique_y0 == 1][0] if 1 in unique_y0 else 0
    negative_y0_count = counts_y0[unique_y0 == 0][0] if 0 in unique_y0 else 0
    print(f"    正例 (label 1) 个数: {positive_y0_count} ({(positive_y0_count / total_y0) * 100:.2f}%)")
    print(f"    负例 (label 0) 个数: {negative_y0_count} ({(negative_y0_count / total_y0) * 100:.2f}%)")
    y1_labels = po_data_np[:, 1]
    unique_y1, counts_y1 = np.unique(y1_labels, return_counts=True)
    total_y1 = len(y1_labels)
    print(f"  {po_name} - Y(1) (第二列):")
    print(f"    总样本数: {total_y1}")
    positive_y1_count = counts_y1[unique_y1 == 1][0] if 1 in unique_y1 else 0
    negative_y1_count = counts_y1[unique_y1 == 0][0] if 0 in unique_y1 else 0
    print(f"    正例 (label 1) 个数: {positive_y1_count} ({(positive_y1_count / total_y1) * 100:.2f}%)")
    print(f"    负例 (label 0) 个数: {negative_y1_count} ({(negative_y1_count / total_y1) * 100:.2f}%)")
    print(f"----------------------------------------------------")


# --- 辅助函数：寻找最优阈值 (使用最大化Youden's J statistic - 确保这是您最终选择的方法) ---
def find_optimal_threshold_youden_j_and_metrics(y_true, y_pred_proba, pos_label=1):
    fpr, tpr, thresholds = roc_curve(y_true, y_pred_proba, pos_label=pos_label)
    youden_j = tpr - fpr
    if len(youden_j) == 0 or len(thresholds) <= np.argmax(youden_j):  # 增加对索引的检查
        optimal_threshold = 0.5
        y_pred_binary = (y_pred_proba >= optimal_threshold).astype(int)
        acc = accuracy_score(y_true, y_pred_binary)
        f1 = f1_score(y_true, y_pred_binary, pos_label=pos_label, zero_division=0)
        rec = recall_score(y_true, y_pred_binary, pos_label=pos_label, zero_division=0)
        return optimal_threshold, acc, f1, rec
    idx_optimal = np.argmax(youden_j)
    optimal_threshold = thresholds[idx_optimal]
    if optimal_threshold > 1.0: optimal_threshold = 1.0
    if optimal_threshold < 0.0: optimal_threshold = 0.0
    y_pred_binary_optimal = (y_pred_proba >= optimal_threshold).astype(int)
    acc = accuracy_score(y_true, y_pred_binary_optimal)
    f1 = f1_score(y_true, y_pred_binary_optimal, pos_label=pos_label, zero_division=0)
    rec = recall_score(y_true, y_pred_binary_optimal, pos_label=pos_label, zero_division=0)
    prec = precision_score(y_true, y_pred_binary_optimal,pos_label=pos_label, zero_division=0)
    return optimal_threshold, acc, f1, rec ,prec


# --- (备选) 寻找最优阈值的辅助函数 (使用最大化F1分数) ---
# def find_optimal_threshold_max_f1_and_metrics(y_true, y_pred_proba, pos_label=1):
#     # ... (函数体与之前提供的一致) ...

def main(args):
    parameters = dict();
    parameters['data_name'] = args.data_name;
    parameters['batch_size'] = args.batch_size;
    parameters['iteration'] = args.iteration;
    parameters['learning_rate'] = args.learning_rate;
    parameters['h_dim'] = args.h_dim;
    parameters['x_dim'] = args.x_dim

    if args.data_name == "ihdp": print(
        "IHDP data processing part needs a model call like 'vganite_ihdp' to be fully functional.")

    if args.data_name == "twin":
        train_x, train_t, train_y, train_potential_y_data, test_x, test_t, test_y_data, test_potential_y_data = data_loading_twin(
            0.8)
        print_potential_outcome_stats(train_potential_y_data, "train_potential_y");
        print_potential_outcome_stats(test_potential_y_data, "test_potential_y")
        if isinstance(test_y_data, tf.Tensor):
            test_y_np = test_y_data.numpy()
        else:
            test_y_np = np.array(test_y_data)
        unique_elements, counts_elements = np.unique(test_y_np, return_counts=True);
        print(f"\n--- test_y (测试集观察结果) 类别统计 ---")
        num_total_test_y = len(test_y_np);
        print(f"Total samples in test_y: {num_total_test_y}")
        positive_count = counts_elements[unique_elements == 1][0] if 1 in unique_elements else 0;
        negative_count = counts_elements[unique_elements == 0][0] if 0 in unique_elements else 0
        print(f"  正例 (label 1) 个数: {positive_count} ({(positive_count / num_total_test_y) * 100:.2f}%)");
        print(f"  负例 (label 0) 个数: {negative_count} ({(negative_count / num_total_test_y) * 100:.2f}%)");
        print(f"---------------------------------------")

        X_train = train_x;
        t_train = np.reshape(train_t, (-1, 1));
        y_train = np.reshape(train_y, (-1, 1))
        X_test = test_x;
        t_test = np.reshape(test_t, (-1, 1));
        y_test_observed = np.reshape(test_y_data, (-1, 1))

        y_hat, ITE_G = vganite_twin(X_train, t_train, y_train, train_potential_y_data, X_test, test_potential_y_data,
                                    y_test_observed, t_test, parameters)

        y_hat = tf.cast(y_hat, tf.float32)
        if not isinstance(test_potential_y_data, tf.Tensor):
            test_potential_y_for_metrics = tf.cast(test_potential_y_data, tf.float32)
        else:
            test_potential_y_for_metrics = tf.cast(test_potential_y_data, tf.float32)
        if not isinstance(train_potential_y_data, tf.Tensor):
            train_potential_y_for_metrics = tf.cast(train_potential_y_data, tf.float32)
        else:
            train_potential_y_for_metrics = tf.cast(train_potential_y_data, tf.float32)

        ate_t = np.mean((test_potential_y_for_metrics[:, 1].numpy()) - (test_potential_y_for_metrics[:, 0].numpy()));
        ate_hat = np.mean((y_hat[:, 1].numpy()) - (y_hat[:, 0].numpy()))
        train_x_tf = tf.cast(train_x, tf.float32);
        train_y_hat_logits = ITE_G(train_x_tf, training=False);
        train_y_hat = tf.nn.sigmoid(train_y_hat_logits)
        train_pehe = PEHE(train_potential_y_for_metrics, train_y_hat);
        train_ate = ATE(train_potential_y_for_metrics, train_y_hat)

        test_potential_y_np = test_potential_y_for_metrics.numpy();
        y_hat_np = y_hat.numpy()

        print("\n--- 分析预测结果的分布 (测试集) ---")
        pred_probas_y0 = y_hat_np[:, 0];
        pred_probas_y1 = y_hat_np[:, 1];
        predicted_ites = y_hat_np[:, 1] - y_hat_np[:, 0]
        true_y0 = test_potential_y_np[:, 0];
        true_y1 = test_potential_y_np[:, 1]
        print("\n预测概率和ITE的统计摘要:");
        print(
            f"  预测 Y(0) 概率: Min={np.min(pred_probas_y0):.4f}, Mean={np.mean(pred_probas_y0):.4f}, Median={np.median(pred_probas_y0):.4f}, Max={np.max(pred_probas_y0):.4f}, StdDev={np.std(pred_probas_y0):.4f}");
        print(
            f"  预测 Y(1) 概率: Min={np.min(pred_probas_y1):.4f}, Mean={np.mean(pred_probas_y1):.4f}, Median={np.median(pred_probas_y1):.4f}, Max={np.max(pred_probas_y1):.4f}, StdDev={np.std(pred_probas_y1):.4f}");
        print(
            f"  预测 ITE:       Min={np.min(predicted_ites):.4f}, Mean={np.mean(predicted_ites):.4f}, Median={np.median(predicted_ites):.4f}, Max={np.max(predicted_ites):.4f}, StdDev={np.std(predicted_ites):.4f}")
        print("\n正在生成预测分布直方图 (保存为文件)...")
        try:
            plt.figure(figsize=(18, 5));
            plt.subplot(1, 3, 1);
            sns.histplot(pred_probas_y0, kde=True, bins=30);
            plt.title('预测 P(Y(0)=1) 的分布');
            plt.xlabel('预测概率');
            plt.ylabel('频数');
            plt.subplot(1, 3, 2);
            sns.histplot(pred_probas_y1, kde=True, bins=30);
            plt.title('预测 P(Y(1)=1) 的分布');
            plt.xlabel('预测概率');
            plt.ylabel('频数');
            plt.subplot(1, 3, 3);
            sns.histplot(predicted_ites, kde=True, bins=30);
            plt.title('预测 ITE (Y(1)-Y(0)) 的分布');
            plt.xlabel('预测ITE');
            plt.ylabel('频数');
            plt.tight_layout();
            plt.savefig('predicted_distributions_overall.png');
            plt.close()
            plt.figure(figsize=(12, 6));
            plt.subplot(1, 2, 1);
            sns.histplot(pred_probas_y0[true_y0 == 0], kde=True, bins=30, color='blue', label='真实 Y(0)=0',
                         element="step", alpha=0.7);
            sns.histplot(pred_probas_y0[true_y0 == 1], kde=True, bins=30, color='red', label='真实 Y(0)=1',
                         element="step", alpha=0.7);
            plt.title('预测 P(Y(0)=1) 的分布 (按真实Y(0)分组)');
            plt.xlabel('预测概率 P(Y(0)=1)');
            plt.ylabel('频数');
            plt.legend();
            plt.subplot(1, 2, 2);
            sns.histplot(pred_probas_y1[true_y1 == 0], kde=True, bins=30, color='blue', label='真实 Y(1)=0',
                         element="step", alpha=0.7);
            sns.histplot(pred_probas_y1[true_y1 == 1], kde=True, bins=30, color='red', label='真实 Y(1)=1',
                         element="step", alpha=0.7);
            plt.title('预测 P(Y(1)=1) 的分布 (按真实Y(1)分组)');
            plt.xlabel('预测概率 P(Y(1)=1)');
            plt.ylabel('频数');
            plt.legend();
            plt.tight_layout();
            plt.savefig('predicted_distributions_by_true_class.png');
            plt.close()
            print("直方图已保存为 predicted_distributions_overall.png 和 predicted_distributions_by_true_class.png")
        except Exception as e:
            print(f"绘制图形时出错: {e}. 请确保已安装 matplotlib 和 seaborn.")

        # 选择阈值函数 (确保这是您想要的函数)
        threshold_func = find_optimal_threshold_youden_j_and_metrics
        # threshold_func = find_optimal_threshold_max_f1_and_metrics # 如果想用F1，解除这个注释
        print(f"\n--- 单个潜在结果预测评估 (优化阈值：{threshold_func.__name__.split('_')[3].upper()}) ---")

        opt_thresh_y0, acc_y0, f1_y0, recall_y0 ,prec_y0= threshold_func(true_y0, pred_probas_y0)
        try:
            auc_y0 = roc_auc_score(true_y0, pred_probas_y0)
        except ValueError as e:
            print(f"无法计算 AUC for Y(0): {e}"); auc_y0 = np.nan
        print(
            f"Y(0) - Optimal Threshold: {opt_thresh_y0:.4f}, Accuracy: {acc_y0:.4f}, precision: {prec_y0:.4f},F1-Score: {f1_y0:.4f}, Recall: {recall_y0:.4f}, AUC: {auc_y0:.4f}")

        opt_thresh_y1, acc_y1, f1_y1, recall_y1 ,prec_y1= threshold_func(true_y1, pred_probas_y1)
        try:
            # !! 仔细检查这里的变量名 !! 应该用 pred_probas_y1
            auc_y1 = roc_auc_score(true_y1, pred_probas_y1)  # 确保这里是 pred_probas_y1
        except ValueError as e:
            print(f"无法计算 AUC for Y(1): {e}")
            auc_y1 = np.nan
        print(
            f"Y(1) - Optimal Threshold: {opt_thresh_y1:.4f}, Accuracy: {acc_y1:.4f}, precision: {prec_y1:.4f},F1-Score: {f1_y1:.4f}, Recall: {recall_y1:.4f}, AUC: {auc_y1:.4f}")

        print("\n--- 单个潜在结果预测评估 (回归类指标) ---")
        mse_y0 = mean_squared_error(test_potential_y_np[:, 0], y_hat_np[:, 0]);
        rmse_y0 = np.sqrt(mse_y0);
        mae_y0 = mean_absolute_error(test_potential_y_np[:, 0], y_hat_np[:, 0])
        print(f"Y(0) - RMSE: {rmse_y0:.4f}, MAE: {mae_y0:.4f}")
        mse_y1 = mean_squared_error(test_potential_y_np[:, 1], y_hat_np[:, 1]);
        rmse_y1 = np.sqrt(mse_y1);
        mae_y1 = mean_absolute_error(test_potential_y_np[:, 1], y_hat_np[:, 1])
        print(f"Y(1) - RMSE: {rmse_y1:.4f}, MAE: {mae_y1:.4f}")

        print("\n--- ITE 核心评估结果 ---")
        print('训练集上的结果：');
        print(f"  训练集_PEHE: {train_pehe:.4f}");
        print(f"  训练集_ATE_Error: {train_ate:.4f}")
        print('测试集上的结果：');
        # print(f"  测试集_True_ATE: {ate_t:.4f}");
        # print(f"  测试集_Predicted_ATE: {ate_hat:.4f}")
        pehe_test = PEHE(test_potential_y_for_metrics, y_hat);
        ate_test = ATE(test_potential_y_for_metrics, y_hat)
        print(f"  测试集_PEHE: {pehe_test:.4f}");
        print(f"  测试集_ATE_Error: {ate_test:.4f}")

    if args.data_name == "syn": print(
        "Syn data processing part needs a model call like 'vganite_syn' to be fully functional.")
    if args.data_name == "syn_no": print(
        "Syn_no data processing part needs a model call like 'vganite_syn_nozc' to be fully functional.")


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--data_name', choices=['twin', 'ihdp', 'syn', 'syn_no'], default='twin', type=str)
    parser.add_argument('--x_dim', choices=[13, 17, 21, 25, 30], default=30, type=int)
    parser.add_argument('--iteration', help='Training iterations (should be optimized)', default=500, type=int)
    parser.add_argument('--batch_size', help='the number of samples in mini-batch (should be optimized)', default=128,
                        type=int)
    parser.add_argument('--learning_rate', choices=[0.001, 0.0001], default=0.001, type=float)
    parser.add_argument('--h_dim', help='hidden state dimensions (should be optimized)', default=15, type=int)
    args = parser.parse_args()
    main(args)