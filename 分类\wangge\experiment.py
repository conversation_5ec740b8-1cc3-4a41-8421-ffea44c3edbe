# experiment.py
import numpy as np, tensorflow as tf
from metrics import PEHE, ATE
from wgangp import vganite_twin
from datasets import data_loading_twin   # 如果你不是 twin，请改为自己的 loader

def run_once(params, seed=123):
    tf.keras.utils.set_random_seed(seed)         # 复现性
    # —— 数据加载 ——（按你 main.py 里 twin 的逻辑来）
    tr_x, tr_t, tr_y, tr_pot_y, te_x, te_t, te_y_obs, te_pot_y = data_loading_twin(0.8)

    # —— 训练 ＆ 预测 ——（核心调用）
    y_hat, _ = vganite_twin(tr_x, tr_t.reshape(-1,1), tr_y.reshape(-1,1),
                            tr_pot_y, te_x, te_pot_y, te_y_obs.reshape(-1,1),
                            te_t.reshape(-1,1), params)

    # —— 评估 ——（主指标：PEHE；辅指标：ATE 误差）
    pehe  = PEHE(tf.constant(te_pot_y, tf.float32), tf.constant(y_hat, tf.float32))
    ate_e = ATE (tf.constant(te_pot_y, tf.float32), tf.constant(y_hat, tf.float32))
    return pehe, ate_e
