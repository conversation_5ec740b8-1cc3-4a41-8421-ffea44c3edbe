# --- START OF FILE combined_vganite_final.py ---

import os

# --- Setup from wgangp.py (Stable) ---
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["TF_CPP_MIN_LOG_LEVEL"] = '3'

import tensorflow as tf

gpus = tf.config.experimental.list_physical_devices('GPU')
if len(gpus) > 0:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        logical_gpus = tf.config.experimental.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPUs")
    except RuntimeError as e:
        print(e)

import tensorflow.keras.layers as layers
# --- Direct import, assuming utils.py exists and is correct ---
from utils import reparameterize, batch_generator, batch_generator_y

from metrics import PEHE, ATE # Needed for Stage 1 logging and potentially final eval
import numpy as np

# --- Main Function Definition ---
def vganite_twin(train_x, train_t, train_y, train_potential_y, test_x, test_potential_y, y_test, test_t, parameters):

    # --- Hyperparameters ---
    batch_size = parameters['batch_size']
    iterations = parameters['iteration'] # Iterations for EACH stage
    h_dim = parameters['h_dim']
    base_learning_rate = parameters['learning_rate'] # LR for Stage 1 & Stage 2 (based on rawvganite)
    x_dim = parameters['x_dim']

    # === STAGE 1: Models defined as in wgangp.py (Stable) ===
    # VAE Class definition remains the same (identical to previous correct version)
    class VAE(tf.keras.Model):
        def __init__(self):
            super(VAE, self).__init__()
            # Enhanced Encoder network with LeakyReLU and BatchNormalization from wgangp.py
            self.fc1 = layers.Dense(128)
            self.bn1 = layers.BatchNormalization()
            self.act1 = layers.LeakyReLU(alpha=0.2)
            self.dropout1 = layers.Dropout(0.3)
            self.fc1_1 = layers.Dense(128)
            self.bn1_1 = layers.BatchNormalization()
            self.act1_1 = layers.LeakyReLU(alpha=0.2)
            self.fc12 = layers.Dense(128)
            self.bn12 = layers.BatchNormalization()
            self.act12 = layers.LeakyReLU(alpha=0.2)
            self.dropout2 = layers.Dropout(0.3)
            self.fc1_2 = layers.Dense(128)

            self.fc2 = layers.Dense(128)
            self.bn2 = layers.BatchNormalization()
            self.act2 = layers.LeakyReLU(alpha=0.2)
            self.dropout3 = layers.Dropout(0.3)
            self.fc2_1 = layers.Dense(128)
            self.bn2_1 = layers.BatchNormalization()
            self.act2_1 = layers.LeakyReLU(alpha=0.2)
            self.fc22 = layers.Dense(128)
            self.bn22 = layers.BatchNormalization()
            self.act22 = layers.LeakyReLU(alpha=0.2)
            self.dropout4 = layers.Dropout(0.3)
            self.fc2_2 = layers.Dense(128)

            self.fc3 = layers.Dense(128)
            self.bn3 = layers.BatchNormalization()
            self.act3 = layers.LeakyReLU(alpha=0.2)
            self.dropout5 = layers.Dropout(0.3)
            self.fc3_1 = layers.Dense(128)
            self.bn3_1 = layers.BatchNormalization()
            self.act3_1 = layers.LeakyReLU(alpha=0.2)
            self.fc32 = layers.Dense(128)
            self.bn32 = layers.BatchNormalization()
            self.act32 = layers.LeakyReLU(alpha=0.2)
            self.dropout6 = layers.Dropout(0.3)
            self.fc3_2 = layers.Dense(128)

            # Enhanced Decoder network from wgangp.py
            self.fc4 = layers.Dense(256)
            self.bn4 = layers.BatchNormalization()
            self.act4 = layers.LeakyReLU(alpha=0.2)
            self.fc4_1 = layers.Dense(128)
            self.bn4_1 = layers.BatchNormalization()
            self.act4_1 = layers.LeakyReLU(alpha=0.2)
            self.fc41 = layers.Dense(x_dim)

            # Treatment predictor from z_t, z_c (from wgangp.py)
            self.fc5 = layers.Dense(64)
            self.bn5 = layers.BatchNormalization()
            self.act5 = layers.LeakyReLU(alpha=0.2)
            self.fc5_1 = layers.Dense(32)
            self.bn5_1 = layers.BatchNormalization()
            self.act5_1 = layers.LeakyReLU(alpha=0.2)
            self.fc51 = layers.Dense(1, activation=tf.nn.sigmoid)

            # Treatment predictor from z_c (for independence) (from wgangp.py)
            self.fc8 = layers.Dense(64)
            self.bn8 = layers.BatchNormalization()
            self.act8 = layers.LeakyReLU(alpha=0.2)
            self.fc8_1 = layers.Dense(32)
            self.bn8_1 = layers.BatchNormalization()
            self.act8_1 = layers.LeakyReLU(alpha=0.2)
            self.fc81 = layers.Dense(1, activation=tf.nn.sigmoid)

            # Enhanced Stage 1 Generator (predicts potential outcomes logits) (from wgangp.py)
            self.G_h1 = layers.Dense(units=h_dim * 4)
            self.G_bn1 = layers.BatchNormalization()
            self.G_act1 = layers.LeakyReLU(alpha=0.2)
            self.dropout7 = layers.Dropout(0.2)
            self.G_h2 = layers.Dense(units=h_dim * 2)
            self.G_bn2 = layers.BatchNormalization()
            self.G_act2 = layers.LeakyReLU(alpha=0.2)
            self.G_h31 = layers.Dense(units=h_dim)
            self.G_bn3 = layers.BatchNormalization()
            self.G_act3 = layers.LeakyReLU(alpha=0.2)
            self.G_logit = layers.Dense(units=2)  # Output [Y(0), Y(1)] logits

        # Encoder/Decoder/treatment/generator/call methods remain the same (identical to previous correct version)
        def encoder(self, x):
            ht = self.fc1(x)
            ht = self.bn1(ht, training=self.training)
            ht = self.act1(ht)
            ht = self.dropout1(ht, training=self.training)
            mu_t = self.fc1_1(ht)
            mu_t = self.bn1_1(mu_t, training=self.training)
            mu_t = self.act1_1(mu_t)
            log_var_t = self.fc12(x)
            log_var_t = self.bn12(log_var_t, training=self.training)
            log_var_t = self.act12(log_var_t)
            log_var_t = self.dropout2(log_var_t, training=self.training)
            log_var_t = self.fc1_2(log_var_t)
            hc = self.fc2(x)
            hc = self.bn2(hc, training=self.training)
            hc = self.act2(hc)
            hc = self.dropout3(hc, training=self.training)
            mu_c = self.fc2_1(hc)
            mu_c = self.bn2_1(mu_c, training=self.training)
            mu_c = self.act2_1(mu_c)
            log_var_c = self.fc22(x)
            log_var_c = self.bn22(log_var_c, training=self.training)
            log_var_c = self.act22(log_var_c)
            log_var_c = self.dropout4(log_var_c, training=self.training)
            log_var_c = self.fc2_2(log_var_c)
            hy = self.fc3(x)
            hy = self.bn3(hy, training=self.training)
            hy = self.act3(hy)
            hy = self.dropout5(hy, training=self.training)
            mu_y = self.fc3_1(hy)
            mu_y = self.bn3_1(mu_y, training=self.training)
            mu_y = self.act3_1(mu_y)
            log_var_y = self.fc32(x)
            log_var_y = self.bn32(log_var_y, training=self.training)
            log_var_y = self.act32(log_var_y)
            log_var_y = self.dropout6(log_var_y, training=self.training)
            log_var_y = self.fc3_2(log_var_y)
            return mu_t, log_var_t, mu_c, log_var_c, mu_y, log_var_y
        def decoder(self, z_t, z_c, z_y):
            Z = tf.concat([z_t, z_c, z_y], axis=1)
            Z = self.fc4(Z)
            Z = self.bn4(Z, training=self.training)
            Z = self.act4(Z)
            x = self.fc4_1(Z)
            x = self.bn4_1(x, training=self.training)
            x = self.act4_1(x)
            x = self.fc41(x)
            return x
        def decoder_t(self, z_t, z_c):
            t_hat = self.fc5(tf.concat([z_t, z_c], axis=1))
            t_hat = self.bn5(t_hat, training=self.training)
            t_hat = self.act5(t_hat)
            t_hat = self.fc5_1(t_hat)
            t_hat = self.bn5_1(t_hat, training=self.training)
            t_hat = self.act5_1(t_hat)
            t_hat = self.fc51(t_hat)
            return t_hat
        def treatment(self, z_c):
            t_hat = self.fc8(z_c)
            t_hat = self.bn8(t_hat, training=self.training)
            t_hat = self.act8(t_hat)
            t_hat = self.fc8_1(t_hat)
            t_hat = self.bn8_1(t_hat, training=self.training)
            t_hat = self.act8_1(t_hat)
            t_hat = self.fc81(t_hat)
            return t_hat
        def generator(self, z_c, z_y):
            inputs = tf.concat([z_c, z_y], axis=1)
            G_h1 = self.G_h1(inputs)
            G_h1 = self.G_bn1(G_h1, training=self.training)
            G_h1 = self.G_act1(G_h1)
            G_h1 = self.dropout7(G_h1, training=self.training)
            G_h2 = self.G_h2(G_h1)
            G_h2 = self.G_bn2(G_h2, training=self.training)
            G_h2 = self.G_act2(G_h2)
            G_h31 = self.G_h31(G_h2)
            G_h31 = self.G_bn3(G_h31, training=self.training)
            G_h31 = self.G_act3(G_h31)
            G_logit = self.G_logit(G_h31)
            return G_logit # [Y(0),Y(1)] logits
        def call(self, inputs, t, y, training=None):
            self.training = training
            mu_t, log_var_t, mu_c, log_var_c, mu_y, log_var_y = self.encoder(inputs)
            z_t = reparameterize(mu_t, log_var_t)
            z_c = reparameterize(mu_c, log_var_c)
            z_y = reparameterize(mu_y, log_var_y)
            z_t = z_t / tf.sqrt(tf.reduce_sum(tf.square(z_t), axis=1, keepdims=True) + 1e-8)
            z_c = z_c / tf.sqrt(tf.reduce_sum(tf.square(z_c), axis=1, keepdims=True) + 1e-8)
            z_y = z_y / tf.sqrt(tf.reduce_sum(tf.square(z_y), axis=1, keepdims=True) + 1e-8)
            x_hat = self.decoder(z_t, z_c, z_y)
            t_hat = self.decoder_t(z_t, z_c)
            y_hat_logits = self.generator(z_c, z_y)
            treat_t_hat = self.treatment(z_c)
            return z_c, z_y, x_hat, t_hat, y_hat_logits, mu_t, log_var_t, mu_c, log_var_c, mu_y, log_var_y, treat_t_hat

    # Discriminator Class definition remains the same (identical to previous correct version)
    class Discriminator(tf.keras.Model):
        def __init__(self):
            super().__init__()
            self.D_h1 = tf.keras.layers.Dense(units=10, activation=tf.nn.relu)
            self.D_h2 = tf.keras.layers.Dense(units=5, activation=tf.nn.relu)
            self.D_logit = tf.keras.layers.Dense(units=1)
        def call(self, t, y, y_hat_logits): # Takes LOGITS as input
            y_hat_prob = tf.nn.sigmoid(y_hat_logits)
            y_hat_prob = tf.reshape(y_hat_prob, [-1, 2])
            y = tf.reshape(y, [-1, 1])
            t = tf.reshape(t, [-1, 1])
            input0 = (1. - t) * y + t * tf.reshape(y_hat_prob[:, 0], [-1, 1])
            input1 = t * y + (1. - t) * tf.reshape(y_hat_prob[:, 1], [-1, 1])
            y_bar = tf.concat([input0, input1], axis=1)
            D_h1 = self.D_h1(y_bar)
            D_h2 = self.D_h2(D_h1)
            D_logit = self.D_logit(D_h2)
            return D_logit

    # === STAGE 2: Model defined as in rawvganite_twin.py ===
    # inference Class definition remains the same (identical to previous correct version)
    class inference(tf.keras.Model):
        def __init__(self):
            super().__init__()
            self.I_h1 = tf.keras.layers.Dense(units=h_dim, activation=tf.nn.relu)
            self.I_h2 = tf.keras.layers.Dense(units=h_dim, activation=tf.nn.relu)
            self.I_h32 = tf.keras.layers.Dense(units=h_dim, activation=tf.nn.relu)
            self.I_logit0 = tf.keras.layers.Dense(units=2) # Output logits
        def call(self, x, training=None):
            I_h1 = self.I_h1(x)
            I_h2 = self.I_h2(I_h1)
            I_h31 = self.I_h32(I_h2)
            I_logit = self.I_logit0(I_h31)
            return I_logit #[Y(0)_logit, Y(1)_logit]

    # --- Instantiate Models ---
    model = VAE() # Stage 1 VAE (Stable version)
    D = Discriminator() # Stage 1 Discriminator (Stable version)
    Inference = inference() # Stage 2 Inference network (Raw version)

    # --- Optimizers ---
    optimizer_vae = tf.keras.optimizers.Adam(learning_rate=base_learning_rate)
    optimizer_d = tf.keras.optimizers.Adam(learning_rate=base_learning_rate)
    optimizer_i = tf.keras.optimizers.Adam(learning_rate=base_learning_rate) # Stage 2 optimizer

    # === STAGE 1: Training Loop exactly as in wgangp.py (Stable) ===
    # Uses for loop for iterations, calls batch_generator inside D and VAE loops.
    print("--- Start Training Stage 1: VAE (Stable) + Discriminator (Stable) ---")
    data_gen_stage1 = batch_generator(train_x, train_t, train_y, batch_size) # Create generator once
    stage1_batch_iterator = iter(data_gen_stage1) # Create iterator

    for it in range(iterations):
        # --- Train Stage 1 Discriminator (from wgangp.py) ---
        d_loss_val = 0.0
        for _ in range(2): # Train D twice per G update
            try:
                 # Get a batch for D training
                x_batch_d, t_batch_d, y_batch_d = next(stage1_batch_iterator)
            except StopIteration: # Restart iterator if dataset is exhausted within the iteration loop
                stage1_batch_iterator = iter(batch_generator(train_x, train_t, train_y, batch_size))
                x_batch_d, t_batch_d, y_batch_d = next(stage1_batch_iterator)

            with tf.GradientTape() as tape_d:
                # VAE forward pass needed for D training uses the D batch
                _, _, _, _, y_hat_logits_batch_for_d, _, _, _, _, _, _, _ = model(x_batch_d, t_batch_d, y_batch_d, training=True)
                D_logit = D(t_batch_d, y_batch_d, y_hat_logits_batch_for_d)
                d_loss = tf.reduce_mean(tf.nn.sigmoid_cross_entropy_with_logits(labels=t_batch_d, logits=D_logit))
                d_loss_val = d_loss
            d_grads = tape_d.gradient(d_loss, D.trainable_variables)
            if all(g is not None for g in d_grads):
               optimizer_d.apply_gradients(grads_and_vars=zip(d_grads, D.trainable_variables))
            # else:
            #    tf.print("Warning: None gradient detected for Stage 1 Discriminator.")

        # --- Train Stage 1 VAE (from wgangp.py) ---
        try:
            # Get a *new* batch for VAE training
            x_batch_g, t_batch_g, y_batch_g = next(stage1_batch_iterator)
        except StopIteration: # Restart iterator if needed
            stage1_batch_iterator = iter(batch_generator(train_x, train_t, train_y, batch_size))
            x_batch_g, t_batch_g, y_batch_g = next(stage1_batch_iterator)

        with tf.GradientTape() as tape_vae:
            # VAE Forward pass uses the G batch
            z_c, z_y, x_hat, t_hat, y_hat_logits, mu_t, log_var_t, mu_c, log_var_c, mu_y, log_var_y, treat_t_hat = model(x_batch_g, t_batch_g, y_batch_g, training=True)
            # Losses calculated on the G batch
            rec_x_loss = tf.reduce_mean(tf.square(x_hat - x_batch_g))
            rec_t_loss = -tf.reduce_mean(t_batch_g * tf.math.log(t_hat + 1e-8) + (1.0 - t_batch_g) * tf.math.log(1.0 - t_hat + 1e-8))
            treat_t_hat_loss = -tf.reduce_mean(t_batch_g * tf.math.log(treat_t_hat + 1e-8) + (1.0 - t_batch_g) * tf.math.log(1.0 - treat_t_hat + 1e-8))
            kl_div_t = tf.reduce_mean(-0.5 * tf.reduce_sum(log_var_t + 1 - mu_t ** 2 - tf.exp(log_var_t), axis=-1))
            kl_div_c = tf.reduce_mean(-0.5 * tf.reduce_sum(log_var_c + 1 - mu_c ** 2 - tf.exp(log_var_c), axis=-1))
            kl_div_y = tf.reduce_mean(-0.5 * tf.reduce_sum(log_var_y + 1 - mu_y ** 2 - tf.exp(log_var_y), axis=-1))
            p_t = tf.cast(tf.reduce_sum(t_batch_g) / tf.cast(tf.shape(t_batch_g)[0], tf.float32) + 1e-8, tf.float32)
            w_t = t_batch_g / (2. * p_t)
            w_c = (1. - t_batch_g) / (2. * (1. - p_t + 1e-8))
            pi_0 = t_batch_g * treat_t_hat + (1.0 - t_batch_g) * (1.0 - treat_t_hat)
            pi_0 = tf.clip_by_value(pi_0, 1e-6, 1.0 - 1e-6)
            sample_weight = 1. * (1. + (1. - pi_0) / pi_0 * (p_t / (1. - p_t + 1e-8)) ** (2. * t_batch_g - 1.)) * (w_t + w_c)
            y_factual_logit = t_batch_g * tf.reshape(y_hat_logits[:, 1], [-1, 1]) + (1. - t_batch_g) * tf.reshape(y_hat_logits[:, 0], [-1, 1])
            G_loss_Factual = tf.reduce_mean(sample_weight * tf.square(y_factual_logit - y_batch_g))
            # Need D logit for G loss - use the G batch
            D_logit_for_G = D(t_batch_g, y_batch_g, y_hat_logits)
            G_loss_GAN = tf.reduce_mean(tf.nn.sigmoid_cross_entropy_with_logits(labels=(1-t_batch_g), logits=D_logit_for_G))
            vae_loss = rec_x_loss + treat_t_hat_loss + \
                       1e-5 * rec_t_loss + \
                       1e-5 * (kl_div_t + kl_div_c + kl_div_y) + \
                       G_loss_GAN + 2 * G_loss_Factual
        vae_grads = tape_vae.gradient(vae_loss, model.trainable_variables)
        if all(g is not None for g in vae_grads):
             optimizer_vae.apply_gradients(zip(vae_grads, model.trainable_variables))
        # else:
        #    tf.print("Warning: None gradient detected for Stage 1 VAE.")

        # --- Logging (Stage 1 - using stable VAE output) ---
        if (it + 1) % 100 == 0 or (it + 1) == iterations: # Log based on iteration count (it starts from 0)
            # Evaluation uses full datasets, not affected by batching strategy
            x_train_eval = tf.cast(train_x, tf.float32)
            t_train_eval = tf.cast(train_t, tf.float32)
            y_train_eval = tf.cast(train_y, tf.float32)
            _, _, _, _, y_hat_train_logits_eval, _, _, _, _, _, _, _ = model(x_train_eval, t_train_eval, y_train_eval, training=False)
            y_hat_train_prob_eval = tf.nn.sigmoid(y_hat_train_logits_eval)
            train_potential_y_tf = tf.cast(train_potential_y, tf.float32)
            train_pehe = PEHE(train_potential_y_tf, y_hat_train_prob_eval)
            train_ate = ATE(train_potential_y_tf, y_hat_train_prob_eval)
            # Use loss values from the *last VAE training step* for logging
            print(f"Stage 1 - Iter: {it+1}/{iterations}, VAE Loss: {vae_loss:.4f}, Factual Loss: {G_loss_Factual:.4f}, D Loss: {d_loss_val:.4f}") # d_loss_val is from last D step
            print(f"  Train (VAE Gen) PEHE: {train_pehe:.4f}, Train ATE: {train_ate:.4f}")

            x_test_eval = tf.cast(test_x, tf.float32)
            t_test_eval = tf.cast(test_t, tf.float32)
            y_test_eval = tf.cast(y_test, tf.float32)
            _, _, _, _, y_hat_test_logits_eval, _, _, _, _, _, _, _ = model(x_test_eval, t_test_eval, y_test_eval, training=False)
            y_hat_test_prob_eval = tf.nn.sigmoid(y_hat_test_logits_eval)
            test_potential_y_tf = tf.cast(test_potential_y, tf.float32)
            test_pehe = PEHE(test_potential_y_tf, y_hat_test_prob_eval)
            test_ate = ATE(test_potential_y_tf, y_hat_test_prob_eval)
            print(f"  Test (VAE Gen) PEHE: {test_pehe:.4f}, Test ATE: {test_ate:.4f}")


    # === STAGE 2: Target Generation and Training Loop exactly as in rawvganite_twin.py ===
    print("\n--- Start Training Stage 2: Inference Model (Raw Method) ---")

    # --- Stage 2 Target Generation (from rawvganite_twin.py) ---
    # This part remains the same - uses the final trained Stage 1 model
    print("Generating targets for Stage 2 using trained Stage 1 VAE...")
    x_full_train = tf.cast(train_x, tf.float32)
    t_full_train = tf.cast(train_t, tf.float32)
    y_full_train = tf.cast(train_y, tf.float32)
    _, _, _, _, y_hat_factual_logits, _, _, _, _, _, _, _ = model(x_full_train, t_full_train, y_full_train, training=False)
    y_hat_factual_target = y_hat_factual_logits # Target for Stage 2 are the logits
    print(f"Shape of Stage 1 target logits for Stage 2: {y_hat_factual_target.shape}")


    # --- Stage 2 Training Loop (from rawvganite_twin.py) ---
    # Uses for loop for iterations, mimicking rawvganite's structure.
    # Assumes batch_generator_y yields batches covering the dataset.
    data_gen_stage2 = batch_generator_y(train_x, train_t, y_hat_factual_target, batch_size) # Create generator once
    stage2_batch_iterator = iter(data_gen_stage2) # Create iterator
    i_loss_last_batch = tf.constant(0.0) # Keep track of last loss for logging

    for it in range(iterations): # Loop for specified number of iterations
        try:
            # Get next batch for Inference training
            x_batch, t_batch, y_hat_target_batch = next(stage2_batch_iterator)
        except StopIteration: # Restart iterator if dataset is exhausted
            stage2_batch_iterator = iter(batch_generator_y(train_x, train_t, y_hat_factual_target, batch_size))
            x_batch, t_batch, y_hat_target_batch = next(stage2_batch_iterator)

        with tf.GradientTape() as tape:
            Y_hat_logit_inf = Inference(x_batch, training=True)
            # Loss calculation exactly as in rawvganite_twin.py
            I_loss1 = tf.reduce_mean(tf.abs(t_batch * Y_hat_logit_inf[:, 1:2] - (t_batch * y_hat_target_batch[:, 1:2])))
            I_loss2 = tf.reduce_mean(tf.abs((1 - t_batch) * Y_hat_logit_inf[:, 0:1] - ((1 - t_batch) * y_hat_target_batch[:, 0:1])))
            I_loss = I_loss1 + I_loss2
            i_loss_last_batch = I_loss # Store loss of current batch

        I_grads = tape.gradient(I_loss, Inference.trainable_variables)
        if all(g is not None for g in I_grads):
             optimizer_i.apply_gradients(grads_and_vars=zip(I_grads, Inference.trainable_variables))
        # else:
        #    tf.print("Warning: None gradient detected for Stage 2 Inference Model.")

        # --- Logging (Stage 2 - exactly as in rawvganite_twin.py) ---
        if (it + 1) % 100 == 0 or (it + 1) == iterations: # Log based on iteration count
            # Use numpy() if i_loss_last_batch is a Tensor
            print('Stage 2 - Iteration: ' + str(it + 1) + '/' + str(iterations) +
                  ', I loss (current batch): ' + str(np.round(i_loss_last_batch.numpy(), 4)))


    # --- Final Prediction & Return (from rawvganite_twin.py) ---
    print("\n--- Finished Training Stage 2 ---")
    return Inference # Return the trained Stage 2 Inference model

# --- END OF FILE combined_vganite_final.py ---