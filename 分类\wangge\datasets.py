import os
os.environ["CUDA_VISIBLE_DEVICES"]="0"
os.environ["TF_CPP_MIN_LOG_LEVEL"]='3'
import tensorflow as tf
import numpy as np
import pandas as pd
from scipy.special import expit
from sklearn.model_selection import train_test_split
from utils import batch_generator
def IHDP(path = './IHDP',reps=1):
    path_data = path
    replications = reps
    # which features are binary
    binfeats = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]
    # which features are continuous
    contfeats = [i for i in range(25) if i not in binfeats]

    data = np.loadtxt(path_data + '/ihdp_npci_train_' + str(replications) + '.csv', delimiter=',', skiprows=1)
    t, y = data[:, 0], data[:, 1][:, np.newaxis]
    mu_0, mu_1, x = data[:, 3][:, np.newaxis], data[:, 4][:, np.newaxis], data[:, 5:]
    true_ite = mu_1 - mu_0
    train_potential_y = tf.concat([mu_0, mu_1], axis=1)
    x[:, 13] -= 1
    # perm = binfeats + contfeats
    # x = x[:, perm]

    x = np.array(x)
    y = np.array(y).squeeze()
    t = np.array(t).squeeze()
    train = (x, t, y), true_ite, train_potential_y

    data_test = np.loadtxt(path_data + '/ihdp_npci_test_' + str(replications) + '.csv', delimiter=',', skiprows=1)
    t_test, y_test = data_test[:, 0][:, np.newaxis], data_test[:, 1][:, np.newaxis]
    mu_0_test, mu_1_test, x_test = data_test[:, 3][:, np.newaxis], data_test[:, 4][:, np.newaxis], data_test[:, 5:]
    x_test[:, 13] -= 1
    # x_test = x_test[:, perm]

    x_test = np.array(x_test)
    y_test = np.array(y_test).squeeze()
    t_test = np.array(t_test).squeeze()

    true_ite_test = mu_1_test - mu_0_test
    test_potential_y = tf.concat([mu_0_test, mu_1_test], axis=1)
    test = (x_test, t_test, y_test), true_ite_test, test_potential_y
    return train, test, contfeats, binfeats



def data_loading_twin(train_rate=0.8, random_state=42): # 可以保留或添加 random_state
    """
    **注意：此函数现在固定执行以下逻辑：**
    1. 从 'Twin/Twin.csv' 加载完整数据。
    2. 基于完整特征生成 '真实' 的 t, y 和 potential_y。
    3. 从 'Twin/0.5.csv' 加载特征 x (包含缺失值)。
    4. 将 x 中的缺失值填充为 0。
    5. 使用处理后的缺失 x 和 '真实' 的 t, y, potential_y 划分训练/测试集。

    原始加载 '0.1.csv' 的逻辑已被替换。

    Args:
        train_rate (float): 训练数据比例。
        random_state (int): 用于 train/test split 的随机种子。

    Returns:
        tuple: train_x, train_t, train_y, train_potential_y,
               test_x, test_t, test_y, test_potential_y
               (x 含填充缺失值, t/y/potential_y 基于完整数据生成)
    """
    complete_file="Twin/Twin.csv"
    missing_x_file="Twin/Twin.csv" # 固定使用 50% 缺失特征文件

    print(f"--- Running modified 'data_loading_twin' ---")
    print(f"   Using complete data from: {complete_file} (for T, Y, PotentialY)")
    print(f"   Using missing X from: {missing_x_file} (filled with 0)")

    # === Step 1 & 2: 加载完整数据并处理 '真实' potential_y ===
    try:
        df_complete = pd.read_csv(complete_file, delimiter=",", skiprows=1, header=None, encoding='utf-8')
        ori_data_complete_raw = df_complete.values.astype(np.float64)
        if ori_data_complete_raw.shape[1] < 32: # 检查列数
             raise ValueError(f"完整数据文件 '{complete_file}' 应至少有32列，实际为 {ori_data_complete_raw.shape[1]}")

        x_complete = ori_data_complete_raw[:, :30].copy()
        potential_y_raw = ori_data_complete_raw[:, 30:32].copy() # 假设第30, 31列是潜在结果
        potential_y_complete_filled = np.nan_to_num(potential_y_raw, nan=0.0) # 填充潜在结果中的NaN（如有）
        potential_y_complete_processed = np.array(potential_y_complete_filled < 9999, dtype=float) # 按阈值转0/1

    except FileNotFoundError:
        print(f"错误：未找到完整数据集文件 '{complete_file}'")
        raise
    except Exception as e:
        print(f"读取或处理完整数据集时出错: {e}")
        raise

    # === Step 3: 使用完整 x 生成 '真实' t 和 y ===
    no, dim = x_complete.shape
    coef = np.random.uniform(-0.001, 0.001, size=[dim, 1])
    prob_temp = expit(np.matmul(x_complete, coef) + np.random.normal(0, 0.001, size=[no, 1]))
    prob_t = prob_temp / (2 * np.mean(prob_temp))
    prob_t[prob_t > 1] = 1
    t_complete = np.random.binomial(1, prob_t, [no, 1]).reshape([no, ])

    y_complete = np.transpose(t_complete) * potential_y_complete_processed[:, 1] + \
                 np.transpose(1 - t_complete) * potential_y_complete_processed[:, 0]
    y_complete = np.reshape(np.transpose(y_complete), [no, ])

    # === Step 4 & 5: 加载缺失 X 数据并处理 ===
    try:
        df_missing = pd.read_csv(missing_x_file, delimiter=",", skiprows=1, header=None, encoding='utf-8')
        ori_data_missing_raw = df_missing.values
        x_missing_raw = ori_data_missing_raw[:, :30].astype(np.float64) # 只取前30列作为特征
        x_missing_filled = np.nan_to_num(x_missing_raw, nan=0.0) # 将 NaN 填充为 0

    except FileNotFoundError:
        print(f"错误：未找到缺失 X 数据集文件 '{missing_x_file}'")
        raise
    except Exception as e:
        print(f"读取或处理缺失 X 数据集时出错: {e}")
        raise

    # === Step 6: 维度健全性检查 ===
    if x_missing_filled.shape != x_complete.shape:
        raise ValueError(f"维度不匹配! 完整 X 维度 {x_complete.shape} != 缺失 X 维度 {x_missing_filled.shape}。文件可能不对应。")
    if len(t_complete) != no or len(y_complete) != no or potential_y_complete_processed.shape[0] != no:
         raise ValueError(f"生成的 t/y/potential_y 行数不匹配。期望 {no} 行。")

    # === Step 7 & 8: 准备最终数组并划分 ===
    print(f"Debug: 划分数据。训练比例: {train_rate}")
    indices = np.arange(no)
    train_idx, test_idx = train_test_split(indices, train_size=train_rate, random_state=random_state)

    # 使用缺失X 和 真实T/Y/PotentialY 进行划分
    train_x = x_missing_filled[train_idx]
    train_t = t_complete[train_idx]
    train_y = y_complete[train_idx]
    train_potential_y = potential_y_complete_processed[train_idx]

    test_x = x_missing_filled[test_idx]
    test_t = t_complete[test_idx] # 保留以维持返回签名一致
    test_y = y_complete[test_idx] # 保留以维持返回签名一致
    test_potential_y = potential_y_complete_processed[test_idx]

    # === Step 9: 最终转换 ===
    test_potential_y_tf = tf.convert_to_tensor(test_potential_y, dtype=tf.float32)

    print(f"--- 数据加载完成: 训练集 {len(train_idx)}, 测试集 {len(test_idx)} ---")

    # === Step 10: 返回 ===
    # 返回值的顺序和数量与 main.py 期望的一致
    return (train_x, train_t, train_y, train_potential_y,
            test_x, test_t, test_y, test_potential_y_tf)


def Syn(path = './data/Syn_1.0_1.0_0/8_8_8',reps=1):
    path_data = path
    replications = reps

    data = np.loadtxt(path_data + '/4_' + str(replications) + '.csv', delimiter=',', skiprows=1)
    t, y = data[:, 0], data[:, 1][:, np.newaxis]
    mu_0, mu_1, x = data[:, 3][:, np.newaxis], data[:, 4][:, np.newaxis], data[:, 5:]
    true_ite = mu_1 - mu_0
    train_potential_y = tf.concat([mu_0, mu_1], axis=1)
    # perm = binfeats + contfeats
    # x = x[:, perm]

    x = np.array(x)
    y = np.array(y).squeeze()
    t = np.array(t).squeeze()
    train = (x, t, y), true_ite, train_potential_y

    data_test = np.loadtxt(path_data + '/4_test.csv', delimiter=',', skiprows=1)
    t_test, y_test = data_test[:, 0][:, np.newaxis], data_test[:, 1][:, np.newaxis]
    mu_0_test, mu_1_test, x_test = data_test[:, 3][:, np.newaxis], data_test[:, 4][:, np.newaxis], data_test[:, 5:]

    x_test = np.array(x_test)
    y_test = np.array(y_test).squeeze()
    t_test = np.array(t_test).squeeze()

    true_ite_test = mu_1_test - mu_0_test
    test_potential_y = tf.concat([mu_0_test, mu_1_test], axis=1)
    test = (x_test, t_test, y_test), true_ite_test, test_potential_y
    return train, test

# train, test = Syn(path = './data/Syn_1.0_1.0_0/8_8_8',reps=1)
# (x_train, t_train, y_train), true_ite_train,train_potential_y = train
# (x_test, t_test, y_test), true_ite_test, test_potential_y = test
# print(train_potential_y.shape)
# print(x_test.shape)