import os
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"]="0"
os.environ["TF_CPP_MIN_LOG_LEVEL"]='3'
import tensorflow as tf
import numpy as np
import argparse

# --- TensorFlow 2.x GPU 配置 ---  <---- 把新代码放在这里
gpus = tf.config.list_physical_devices('GPU')
if gpus:
  try:
    # 设置内存增长 (推荐)
    for gpu in gpus:
      tf.config.experimental.set_memory_growth(gpu, True)
    logical_gpus = tf.config.list_logical_devices('GPU')
    print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPUs")
  except RuntimeError as e:
    print("GPU configuration error:", e)

# from vganite_ihdp import vganite_ihdp
from rawvganite_twin import vganite_twin
# from vganite_syn import vganite_syn
# from vganite_syn_nozt import vganite_syn_nozt
# from vganite_syn_nozc import vganite_syn_nozc
# from vganite_syn_nozy import vganite_syn_nozy


from  metrics import PEHE,ATE
from datasets import IHDP,data_loading_twin,Syn



def main(args):
    parameters = dict()
    parameters['data_name'] = args.data_name
    parameters['batch_size'] = args.batch_size
    parameters['iteration'] = args.iteration
    parameters['learning_rate'] = args.learning_rate
    parameters['h_dim'] = args.h_dim
    parameters['x_dim'] = args.x_dim


    if args.data_name =="ihdp":
        train, test, contfeats, binfeats = IHDP(path="IHDP", reps=12)
        (x_train, t_train, y_train), true_ite_train,train_potential_y = train
        (x_test, t_test, y_test), true_ite_test,test_potential_y = test

        X_train = x_train
        t_train = np.reshape(t_train,(673,1))
        y_train = np.reshape(y_train,(673,1))

        X_test = x_test
        t_test = np.reshape(t_test,(74,1))
        y_test = np.reshape(y_test,(74,1))
        y_hat = vganite_ihdp(X_train, t_train, y_train, train_potential_y, X_test, test_potential_y,y_test,t_test, parameters)
        y_hat = tf.cast(y_hat,tf.float32)
        test_potential_y = tf.cast(test_potential_y,tf.float32)
        ate_t = np.mean((test_potential_y[:, 1]) - (test_potential_y[:, 0]))
        ate_hat = np.mean((y_hat[:, 1]) - (y_hat[:, 0]))
        print('Test_T_ATE',ate_t)
        print('Test_hat_ATE', ate_hat)
        print("Test_PEHE:", PEHE(test_potential_y, y_hat))
        print("Test_ATE:", ATE(test_potential_y, y_hat))
    if args.data_name == "twin":
        train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = data_loading_twin(0.8)

        # 准备训练数据
        X_train = train_x
        t_train = np.reshape(train_t, (9120, 1))
        y_train = np.reshape(train_y, (9120, 1))

        # 准备测试数据
        X_test = test_x
        t_test = np.reshape(test_t, (2280, 1))
        y_test = np.reshape(test_y, (2280, 1))

        # 调用 vganite_twin 获取训练好的 Inference 模型
        # 假设 vganite_twin 已修改为返回模型本身
        trained_inference_model = vganite_twin(X_train, t_train, y_train, train_potential_y,
                                               X_test, test_potential_y, y_test, t_test,
                                               parameters)

        # --- 评估测试集 ---
        print("\n--- Evaluating on Test Set ---")
        # 使用模型在测试集上预测原始 logits
        y_hat_logits_test = trained_inference_model(tf.cast(X_test, tf.float32))

        # ***** 修改开始: 应用 Sigmoid *****
        # 将 logits 转换为 [0, 1] 范围内的估计值
        y_hat_test = tf.sigmoid(y_hat_logits_test)
        # ***** 修改结束 *****

        # 确保 y_hat_test 和 potential_y 的类型一致 (通常 Sigmoid 输出已经是 float32)
        # y_hat_test = tf.cast(y_hat_test, tf.float32) # 可能不需要，取决于 tf.sigmoid 的行为
        test_potential_y_tensor = tf.cast(test_potential_y, tf.float32)

        # 使用 [0, 1] 范围内的 y_hat_test 计算指标
        ate_t_test = np.mean((test_potential_y_tensor[:, 1]) - (test_potential_y_tensor[:, 0]))
        ate_hat_test = np.mean((y_hat_test[:, 1]) - (y_hat_test[:, 0]))  # 现在 y_hat_test 在 [0,1] 内
        print('Test_T_ATE:', ate_t_test)
        print('Test_hat_ATE:', ate_hat_test)
        print("Test_PEHE:", PEHE(test_potential_y_tensor, y_hat_test))  # PEHE 使用 sigmoid 后的值
        print("Test_ATE:", ATE(test_potential_y_tensor, y_hat_test))  # ATE 使用 sigmoid 后的值

        # --- 评估训练集 ---
        print("\n--- Evaluating on Training Set ---")
        # 使用模型在训练集上预测原始 logits
        y_hat_logits_train = trained_inference_model(tf.cast(X_train, tf.float32))

        # ***** 修改开始: 应用 Sigmoid *****
        # 将 logits 转换为 [0, 1] 范围内的估计值
        y_hat_train = tf.sigmoid(y_hat_logits_train)
        # ***** 修改结束 *****

        # 确保 y_hat_train 和 potential_y 的类型一致
        # y_hat_train = tf.cast(y_hat_train, tf.float32) # 可能不需要
        train_potential_y_tensor = tf.cast(train_potential_y, tf.float32)

        # 使用 [0, 1] 范围内的 y_hat_train 计算指标
        print("Train_PEHE:", PEHE(train_potential_y_tensor, y_hat_train))  # PEHE 使用 sigmoid 后的值

        # (可选) 计算并打印训练集上的 ATE 指标
        ate_t_train = np.mean((train_potential_y_tensor[:, 1]) - (train_potential_y_tensor[:, 0]))
        ate_hat_train = np.mean((y_hat_train[:, 1]) - (y_hat_train[:, 0]))  # 现在 y_hat_train 在 [0,1] 内
        print('Train_T_ATE:', ate_t_train)
        print('Train_hat_ATE:', ate_hat_train)
        print("Train_ATE:", ATE(train_potential_y_tensor, y_hat_train))  # ATE 使用 sigmoid 后的值
    if args.data_name =="syn":
        train, test = Syn(path = './data/Syn_1.0_1.0_0/8_8_4',reps=1)
        (x_train, t_train, y_train), true_ite_train, train_potential_y = train
        (x_test, t_test, y_test), true_ite_test, test_potential_y = test

        X_train = x_train
        t_train = np.reshape(t_train, (14999, 1))
        y_train = np.reshape(y_train, (14999, 1))

        X_test = x_test
        t_test = np.reshape(t_test, (4999, 1))
        y_test = np.reshape(y_test, (4999, 1))
        y_hat = vganite_syn(X_train, t_train, y_train, train_potential_y, X_test, test_potential_y, y_test, t_test,
                             parameters)
        y_hat = tf.cast(y_hat, tf.float32)
        test_potential_y = tf.cast(test_potential_y, tf.float32)
        ate_t = np.mean((test_potential_y[:, 1]) - (test_potential_y[:, 0]))
        ate_hat = np.mean((y_hat[:, 1]) - (y_hat[:, 0]))
        print('Test_T_ATE', ate_t)
        print('Test_hat_ATE', ate_hat)
        print("Test_PEHE:", PEHE(test_potential_y, y_hat))
        print("Test_ATE:", ATE(test_potential_y, y_hat))
    if args.data_name =="syn_no":
        train, test = Syn(path = './data/Syn_1.0_1.0_0/8_8_4',reps=1)
        (x_train, t_train, y_train), true_ite_train, train_potential_y = train
        (x_test, t_test, y_test), true_ite_test, test_potential_y = test

        X_train = x_train
        t_train = np.reshape(t_train, (14999, 1))
        y_train = np.reshape(y_train, (14999, 1))

        X_test = x_test
        t_test = np.reshape(t_test, (4999, 1))
        y_test = np.reshape(y_test, (4999, 1))
        y_hat = vganite_syn_nozc(X_train, t_train, y_train, train_potential_y, X_test, test_potential_y, y_test, t_test,
                             parameters)
        y_hat = tf.cast(y_hat, tf.float32)
        test_potential_y = tf.cast(test_potential_y, tf.float32)
        ate_t = np.mean((test_potential_y[:, 1]) - (test_potential_y[:, 0]))
        ate_hat = np.mean((y_hat[:, 1]) - (y_hat[:, 0]))
        print('Test_T_ATE', ate_t)
        print('Test_hat_ATE', ate_hat)
        print("Test_PEHE:", PEHE(test_potential_y, y_hat))
        print("Test_ATE:", ATE(test_potential_y, y_hat))

if __name__ == '__main__':
    # Inputs for the main function
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--data_name',
        choices=['twin', 'ihdp','syn','syn_no'],
        default='twin',
        type=str)
    parser.add_argument(
        '--x_dim',
        choices=[13,17,21,25,30],
        default=30,
        type=int)
    parser.add_argument(
        '--iteration',
        help='Training iterations (should be optimized)',
        default=500,
        type=int)
    parser.add_argument(
        '--batch_size',
        help='the number of samples in mini-batch (should be optimized)',
        default=128,
        type=int)
    parser.add_argument(
        '--learning_rate',
        choices=[0.001,0.0001],
        default=0.001,
        type=float
    )
    parser.add_argument(  # s in paper
        '--h_dim',
        help='hidden state dimensions (should be optimized)',
        default=5,
        type=int)
    args = parser.parse_args()
    main(args)