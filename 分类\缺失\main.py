import os

os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["TF_CPP_MIN_LOG_LEVEL"] = '3'
import tensorflow as tf
import numpy as np
import argparse
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, mean_squared_error, mean_absolute_error, roc_curve, \
    recall_score

# --- TensorFlow 2.x GPU 配置 ---
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        logical_gpus = tf.config.list_logical_devices('GPU')
        print(len(gpus), "Physical GPUs,", len(logical_gpus), "Logical GPUs")
    except RuntimeError as e:
        print("GPU configuration error:", e)

from wgangp import vganite_twin
from metrics import PEHE, ATE,policy_selection_accuracy
from datasets import IHDP, data_loading_twin, Syn


# --- 辅助函数：打印潜在结果的类别统计 (保持不变) ---
def print_potential_outcome_stats(po_data, po_name):
    if isinstance(po_data, tf.Tensor):
        po_data_np = po_data.numpy()
    else:
        po_data_np = np.array(po_data)

    if po_data_np.ndim != 2 or po_data_np.shape[1] != 2:
        print(f"错误: {po_name} 的期望维度是 (N, 2)，实际是 {po_data_np.shape}")
        return

    print(f"\n--- {po_name} (潜在结果 Y(0) 和 Y(1)) 类别统计 ---")

    y0_labels = po_data_np[:, 0]
    unique_y0, counts_y0 = np.unique(y0_labels, return_counts=True)
    total_y0 = len(y0_labels)
    print(f"  {po_name} - Y(0) (第一列):")
    print(f"    总样本数: {total_y0}")
    positive_y0_count = counts_y0[unique_y0 == 1][0] if 1 in unique_y0 else 0
    negative_y0_count = counts_y0[unique_y0 == 0][0] if 0 in unique_y0 else 0
    print(f"    正例 (label 1) 个数: {positive_y0_count} ({(positive_y0_count / total_y0) * 100:.2f}%)")
    print(f"    负例 (label 0) 个数: {negative_y0_count} ({(negative_y0_count / total_y0) * 100:.2f}%)")

    y1_labels = po_data_np[:, 1]
    unique_y1, counts_y1 = np.unique(y1_labels, return_counts=True)
    total_y1 = len(y1_labels)
    print(f"  {po_name} - Y(1) (第二列):")
    print(f"    总样本数: {total_y1}")
    positive_y1_count = counts_y1[unique_y1 == 1][0] if 1 in unique_y1 else 0
    negative_y1_count = counts_y1[unique_y1 == 0][0] if 0 in unique_y1 else 0
    print(f"    正例 (label 1) 个数: {positive_y1_count} ({(positive_y1_count / total_y1) * 100:.2f}%)")
    print(f"    负例 (label 0) 个数: {negative_y1_count} ({(negative_y1_count / total_y1) * 100:.2f}%)")
    print(f"----------------------------------------------------")


# --- 修改后的辅助函数：寻找最优阈值 (使用最大化Youden's J statistic) ---
def find_optimal_threshold_youden_j_and_metrics(y_true, y_pred_proba, pos_label=1):
    """
    Finds the optimal threshold by maximizing Youden's J statistic,
    and calculates accuracy, F1-score, and recall using this threshold.

    Args:
        y_true (np.array): True binary labels.
        y_pred_proba (np.array): Predicted probabilities for the positive class.
        pos_label (int): The label of the positive class.

    Returns:
        tuple: (optimal_threshold, accuracy, f1, recall)
    """
    fpr, tpr, thresholds = roc_curve(y_true, y_pred_proba, pos_label=pos_label)

    # Calculate Youden's J statistic: J = TPR - FPR
    # (Sensitivity + Specificity - 1 = TPR + (1-FPR) - 1 = TPR - FPR)
    youden_j = tpr - fpr

    if len(youden_j) == 0:  # Edge case if roc_curve returns empty results
        optimal_threshold = 0.5
        y_pred_binary = (y_pred_proba >= optimal_threshold).astype(int)
        acc = accuracy_score(y_true, y_pred_binary)
        f1 = f1_score(y_true, y_pred_binary, pos_label=pos_label, zero_division=0)
        rec = recall_score(y_true, y_pred_binary, pos_label=pos_label, zero_division=0)
        return optimal_threshold, acc, f1, rec

    # Find the index of the maximum Youden's J statistic
    idx_optimal = np.argmax(youden_j)
    optimal_threshold = thresholds[idx_optimal]

    # Clip threshold to be between 0 and 1 if necessary
    # roc_curve thresholds can sometimes be outside [0,1] or be inf/-inf
    if optimal_threshold > 1.0: optimal_threshold = 1.0
    if optimal_threshold < 0.0: optimal_threshold = 0.0
    # Note: if optimal_threshold is inf, (y_pred_proba >= inf) results in all False.
    # If optimal_threshold is -inf, (y_pred_proba >= -inf) results in all True.
    # This behavior is generally handled correctly by the comparison.

    y_pred_binary_optimal = (y_pred_proba >= optimal_threshold).astype(int)
    acc = accuracy_score(y_true, y_pred_binary_optimal)
    f1 = f1_score(y_true, y_pred_binary_optimal, pos_label=pos_label, zero_division=0)
    rec = recall_score(y_true, y_pred_binary_optimal, pos_label=pos_label, zero_division=0)
    # TPR at this point is tpr[idx_optimal], which should be equal to 'rec'

    return optimal_threshold, acc, f1, rec


def main(args):
    parameters = dict()
    parameters['data_name'] = args.data_name
    parameters['batch_size'] = args.batch_size
    parameters['iteration'] = args.iteration
    parameters['learning_rate'] = args.learning_rate
    parameters['h_dim'] = args.h_dim
    parameters['x_dim'] = args.x_dim

    if args.data_name == "ihdp":
        # ... (IHDP 部分代码保持不变) ...
        print("IHDP data processing part needs a model call like 'vganite_ihdp' to be fully functional.")

    if args.data_name == "twin":
        train_x, train_t, train_y, train_potential_y_data, test_x, test_t, test_y_data, test_potential_y_data = data_loading_twin(
            0.8)

        print_potential_outcome_stats(train_potential_y_data, "train_potential_y")
        print_potential_outcome_stats(test_potential_y_data, "test_potential_y")

        if isinstance(test_y_data, tf.Tensor):
            test_y_np = test_y_data.numpy()
        else:
            test_y_np = np.array(test_y_data)
        unique_elements, counts_elements = np.unique(test_y_np, return_counts=True)
        print(f"\n--- test_y (测试集观察结果) 类别统计 ---")
        num_total_test_y = len(test_y_np)
        print(f"Total samples in test_y: {num_total_test_y}")
        positive_count = counts_elements[unique_elements == 1][0] if 1 in unique_elements else 0
        negative_count = counts_elements[unique_elements == 0][0] if 0 in unique_elements else 0
        print(f"  正例 (label 1) 个数: {positive_count} ({(positive_count / num_total_test_y) * 100:.2f}%)")
        print(f"  负例 (label 0) 个数: {negative_count} ({(negative_count / num_total_test_y) * 100:.2f}%)")
        print(f"---------------------------------------")

        X_train = train_x
        t_train = np.reshape(train_t, (-1, 1))
        y_train = np.reshape(train_y, (-1, 1))

        X_test = test_x
        t_test = np.reshape(test_t, (-1, 1))
        y_test_observed = np.reshape(test_y_data, (-1, 1))

        y_hat, ITE_G = vganite_twin(X_train, t_train, y_train, train_potential_y_data,
                                    X_test, test_potential_y_data,
                                    y_test_observed, t_test, parameters)

        y_hat = tf.cast(y_hat, tf.float32)
        if not isinstance(test_potential_y_data, tf.Tensor):
            test_potential_y_for_metrics = tf.cast(test_potential_y_data, tf.float32)
        else:
            test_potential_y_for_metrics = tf.cast(test_potential_y_data, tf.float32)

        ate_t = np.mean((test_potential_y_for_metrics[:, 1].numpy()) - (test_potential_y_for_metrics[:, 0].numpy()))
        ate_hat = np.mean((y_hat[:, 1].numpy()) - (y_hat[:, 0].numpy()))

        train_x_tf = tf.cast(train_x, tf.float32)
        train_y_hat_logits = ITE_G(train_x_tf, training=False)
        train_y_hat = tf.nn.sigmoid(train_y_hat_logits)

        if not isinstance(train_potential_y_data, tf.Tensor):
            train_potential_y_for_metrics = tf.cast(train_potential_y_data, tf.float32)
        else:
            train_potential_y_for_metrics = tf.cast(train_potential_y_data, tf.float32)

        train_pehe = PEHE(train_potential_y_for_metrics, train_y_hat)
        train_ate = ATE(train_potential_y_for_metrics, train_y_hat)

        test_potential_y_np = test_potential_y_for_metrics.numpy()
        y_hat_np = y_hat.numpy()

        # 更新打印信息以反映新的阈值选择方法
        print("\n--- 单个潜在结果预测评估 (优化阈值：最大化Youden's J) ---")

        true_y0 = test_potential_y_np[:, 0]
        pred_proba_y0 = y_hat_np[:, 0]
        # 调用新的辅助函数
        opt_thresh_y0, acc_y0, f1_y0, recall_y0 = find_optimal_threshold_youden_j_and_metrics(true_y0, pred_proba_y0)
        try:
            auc_y0 = roc_auc_score(true_y0, pred_proba_y0)
        except ValueError as e:
            print(f"无法计算 AUC for Y(0): {e} - 可能因为真实标签只有一类。")
            auc_y0 = np.nan
        print(
            f"Y(0) - Optimal Threshold (max Youden's J): {opt_thresh_y0:.4f}, Accuracy: {acc_y0:.4f}, F1-Score (pos_label=1): {f1_y0:.4f}, Recall (pos_label=1): {recall_y0:.4f}, AUC: {auc_y0:.4f}")

        true_y1 = test_potential_y_np[:, 1]
        pred_proba_y1 = y_hat_np[:, 1]
        # 调用新的辅助函数
        opt_thresh_y1, acc_y1, f1_y1, recall_y1 = find_optimal_threshold_youden_j_and_metrics(true_y1, pred_proba_y1)
        try:
            auc_y1 = roc_auc_score(true_y1, pred_proba_y1)
        except ValueError as e:
            print(f"无法计算 AUC for Y(1): {e} - 可能因为真实标签只有一类。")
            auc_y1 = np.nan
        print(
            f"Y(1) - Optimal Threshold (max Youden's J): {opt_thresh_y1:.4f}, Accuracy: {acc_y1:.4f}, F1-Score (pos_label=1): {f1_y1:.4f}, Recall (pos_label=1): {recall_y1:.4f}, AUC: {auc_y1:.4f}")

        print("\n--- 单个潜在结果预测评估 (回归类指标) ---")
        mse_y0 = mean_squared_error(test_potential_y_np[:, 0], y_hat_np[:, 0])
        rmse_y0 = np.sqrt(mse_y0)
        mae_y0 = mean_absolute_error(test_potential_y_np[:, 0], y_hat_np[:, 0])
        print(f"Y(0) - RMSE: {rmse_y0:.4f}, MAE: {mae_y0:.4f}")
        mse_y1 = mean_squared_error(test_potential_y_np[:, 1], y_hat_np[:, 1])
        rmse_y1 = np.sqrt(mse_y1)
        mae_y1 = mean_absolute_error(test_potential_y_np[:, 1], y_hat_np[:, 1])
        print(f"Y(1) - RMSE: {rmse_y1:.4f}, MAE: {mae_y1:.4f}")
        psa = policy_selection_accuracy(
            test_potential_y_for_metrics.numpy(),  # ← 关键改动
            y_hat if isinstance(y_hat, np.ndarray) else y_hat.numpy()
        )

        print(f"Policy Selection Accuracy: {psa:.4f}")

        print("\n--- ITE 核心评估结果 ---")
        print('训练集上的结果：')
        print(f"  训练集_PEHE: {train_pehe:.4f}")
        print(f"  训练集_ATE: {train_ate:.4f}")
        print('测试集上的结果：')
        pehe_test = PEHE(test_potential_y_for_metrics, y_hat)
        ate_test = ATE(test_potential_y_for_metrics, y_hat)
        print(f"  测试集_PEHE: {pehe_test:.4f}")
        print(f"  测试集_ATE_Error: {ate_test:.4f}")

    if args.data_name == "syn":
        print("Syn data processing part needs a model call like 'vganite_syn' to be fully functional.")
    if args.data_name == "syn_no":
        print("Syn_no data processing part needs a model call like 'vganite_syn_nozc' to be fully functional.")


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--data_name', choices=['twin', 'ihdp', 'syn', 'syn_no'], default='twin', type=str)
    parser.add_argument('--x_dim', choices=[13, 17, 21, 25, 30], default=30, type=int)
    parser.add_argument('--iteration', help='Training iterations (should be optimized)', default=500, type=int)
    parser.add_argument('--batch_size', help='the number of samples in mini-batch (should be optimized)', default=128,
                        type=int)
    parser.add_argument('--learning_rate', choices=[0.001, 0.0001], default=0.001, type=float)
    parser.add_argument('--h_dim', help='hidden state dimensions (should be optimized)', default=5, type=int)
    args = parser.parse_args()
    main(args)