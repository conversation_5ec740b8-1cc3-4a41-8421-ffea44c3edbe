import numpy
import numpy as np


def PEHE(y, y_hat):

    PEHE_val = np.sqrt(np.mean(np.square((y[:, 1] - y[:, 0]) - (y_hat[:, 1] - y_hat[:, 0]))))
    return PEHE_val


# def PEHE(y, y_hat):
#
#     PEHE_val = np.mean(np.abs((y[:, 1] - y[:, 0]) - (y_hat[:, 1] - y_hat[:, 0])))
#     PEHE_val = np.sqrt(PEHE_val)
#     return PEHE_val


def ATE(y,y_hat):
    ATE_val = np.abs(np.mean(y[:, 1] - y[:, 0]) - np.mean(y_hat[:, 1] - y_hat[:, 0]))
    return ATE_val

def policy_selection_accuracy(y_true_pot, y_pred_pot):
    """
    y_true_pot : ndarray, shape = (n, 2)      # 列 0: y^0  列 1: y^1  （真实潜在结果）
    y_pred_pot : ndarray, shape = (n, 2)      # 列 0: y^0_hat, 列 1: y^1_hat

    return : float  0~1，越大越好
    """
    # 1) 根据预测潜在结果选“死亡概率更低”的治疗
    #    y=1 表示死亡，0 表示存活 ⇒ 值越小越好
    t_hat = (y_pred_pot[:, 1] < y_pred_pot[:, 0]).astype(int)   # 1 代表 treatment=1，0 代表 treatment=0
    # 若预测相等，可保持现有治疗或随机；此处默认选 treatment=0
    # t_hat = np.where(y_pred_pot[:,1] < y_pred_pot[:,0], 1, 0)

    # 2) 用真实潜在结果判断预测是否正确
    best_true = np.argmin(y_true_pot, axis=1)   # 0 或 1，取较小死亡标记的 treatment

    # 3) 计算平均正确率
    return np.mean(t_hat == best_true)